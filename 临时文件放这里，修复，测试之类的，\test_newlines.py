#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from core.txt_clear import enhanced_content_filter

# 测试文本
test_text = """<div>
<h1>测试标题</h1>

<p>第一段内容。</p>

<p>第二段内容。</p>

<div class="meta">发布时间：2025-01-11</div>

<p>第三段内容。</p>
</div>"""

print("原始文本:")
print("-" * 50)
print(test_text)
print("-" * 50)
print(f"换行符数量: {test_text.count('\\n')}")

# 处理文本
result = enhanced_content_filter(test_text)

print("\n处理后文本:")
print("-" * 50)
print(result)
print("-" * 50)
print(f"换行符数量: {result.count('\\n')}")

print("\n测试完成!")
