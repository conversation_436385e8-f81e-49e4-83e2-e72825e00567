#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复所有 'modules' 引用问题
确保所有地方都正确使用模组管理器
"""

import os
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_modules_references():
    """修复所有文件中的 modules 引用问题"""
    
    # 需要修复的模式
    patterns_to_fix = [
        # 错误的引用模式 -> 正确的引用模式
        (r'modules\.manager\.', 'module_manager.'),
        (r'from modules import manager\s*\n\s*modules\.manager\.', 'from modules.manager import module_manager\nmodule_manager.'),
        (r'from modules import manager\s*\n.*?modules\.manager\.', 'from modules.manager import module_manager\nmodule_manager.'),
    ]
    
    # 需要检查的文件
    files_to_check = [
        'gui/main_window.py',
        'gui/crawler_thread.py',
        'ai/helper.py',
        'ai/analyzer.py',
        'testing/selectors_test.py',
        'core/crawler.py',
        'core/failed_url_processor.py'
    ]
    
    fixed_count = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 检查是否有 modules.manager 引用
                if 'modules.manager' in content:
                    logger.info(f"发现需要修复的引用: {file_path}")
                    
                    # 简单替换
                    content = content.replace('modules.manager.', 'module_manager.')
                    
                    # 修复导入语句
                    content = re.sub(
                        r'from modules import manager\s*\n(\s*)([^=]*?)module_manager\.',
                        r'from modules.manager import module_manager\n\1\2module_manager.',
                        content,
                        flags=re.MULTILINE | re.DOTALL
                    )
                
                # 如果内容有变化，写回文件
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.info(f"修复文件: {file_path}")
                    fixed_count += 1
                    
            except Exception as e:
                logger.error(f"处理文件失败 {file_path}: {e}")
    
    logger.info(f"总共修复了 {fixed_count} 个文件的模组引用")

def test_imports():
    """测试所有导入是否正常"""
    print("\n🔧 测试导入")
    print("="*50)
    
    test_modules = [
        ('gui.main_window', 'CrawlerGUI'),
        ('gui.crawler_thread', 'CrawlerThread'),
        ('modules.manager', 'module_manager'),
        ('ai.helper', 'EnhancedAIConfigManager'),
        ('ai.analyzer', 'AIAnalyzerWithTesting'),
        ('testing.selectors_test', 'SelectorsTestManager')
    ]
    
    success_count = 0
    
    for module_name, class_name in test_modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}.{class_name}: {e}")
    
    print(f"\n导入测试结果: {success_count}/{len(test_modules)} 成功")
    return success_count == len(test_modules)

def test_module_manager():
    """测试模组管理器功能"""
    print("\n📦 测试模组管理器")
    print("="*50)
    
    try:
        from modules.manager import module_manager
        
        # 测试基本功能
        modules = module_manager.list_modules()
        print(f"✅ list_modules(): {len(modules)} 个模组")
        
        # 测试重新加载
        module_manager.load_modules()
        print("✅ load_modules(): 重新加载成功")
        
        # 测试URL匹配
        test_url = "http://www.zhzx.gov.cn/zwhgz/jjwkjw/"
        matched = module_manager.match_url(test_url)
        print(f"✅ match_url(): {matched}")
        
        # 测试便捷函数
        from modules.manager import match_module_for_url, get_config_for_url
        matched_func = match_module_for_url(test_url)
        config_func = get_config_for_url(test_url)
        print(f"✅ 便捷函数正常工作")
        
        return True
        
    except Exception as e:
        print(f"❌ 模组管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_startup():
    """测试GUI启动"""
    print("\n🖥️ 测试GUI启动")
    print("="*50)
    
    try:
        # 只测试导入，不实际启动GUI
        from gui.main_window import CrawlerGUI
        print("✅ GUI主窗口类导入成功")
        
        # 测试线程管理器
        from gui.crawler_thread import CrawlerThreadManager
        thread_manager = CrawlerThreadManager()
        print("✅ 爬虫线程管理器创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复所有 'modules' 引用问题")
    print("="*80)
    
    # 1. 修复引用问题
    fix_modules_references()
    
    # 2. 测试导入
    import_test = test_imports()
    
    # 3. 测试模组管理器
    module_test = test_module_manager()
    
    # 4. 测试GUI启动
    gui_test = test_gui_startup()
    
    # 5. 汇总结果
    print("\n" + "="*80)
    print("🎯 修复结果汇总")
    print("="*80)
    
    if import_test and module_test and gui_test:
        print("✅ 所有 'modules' 引用问题已修复！")
        print("✅ 导入测试通过")
        print("✅ 模组管理器正常工作")
        print("✅ GUI组件正常工作")
        print("\n🚀 现在可以正常启动应用了")
        print("运行: python main.py")
    else:
        print("❌ 仍有问题需要解决")
        if not import_test:
            print("  - 导入测试失败")
        if not module_test:
            print("  - 模组管理器有问题")
        if not gui_test:
            print("  - GUI组件有问题")

if __name__ == "__main__":
    main()
