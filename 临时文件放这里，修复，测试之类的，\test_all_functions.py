#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全功能测试脚本
逐一测试各个功能模块
"""

import sys
import os
import asyncio
import logging
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FunctionTester:
    """功能测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.test_url = "http://www.zhzx.gov.cn/zwhgz/jjwkjw/"
    
    def test_config_management(self) -> bool:
        """测试配置管理功能"""
        print("🔧 测试配置管理功能")
        print("="*50)
        
        try:
            # 测试统一配置管理器
            from config.unified_manager import unified_config_manager
            
            # 测试加载配置
            app_config = unified_config_manager.get_app_config()
            module_configs = unified_config_manager.get_module_configs()
            ai_config = unified_config_manager.get_ai_config()
            
            print("✅ 统一配置管理器加载成功")
            print(f"  - 应用配置: {len(app_config)} 项")
            print(f"  - 模组配置: {len(module_configs)} 项")
            print(f"  - AI配置: {len(ai_config)} 项")
            
            # 测试原有配置管理器
            from config.manager import ConfigManager
            config_manager = ConfigManager()
            groups = config_manager.get_groups()
            print(f"✅ 原配置管理器: {len(groups)} 个配置组")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置管理测试失败: {e}")
            return False
    
    def test_module_management(self) -> bool:
        """测试模组管理功能"""
        print("\n📦 测试模组管理功能")
        print("="*50)
        
        try:
            from modules.manager import ModuleManager
            
            module_manager = ModuleManager()
            modules = module_manager.list_modules()
            
            print(f"✅ 模组管理器加载成功")
            print(f"  - 可用模组: {len(modules)} 个")
            
            for module_name in modules:
                module_info = module_manager.get_module_info(module_name)
                print(f"  - {module_name}: {module_info.get('description', '无描述')}")
            
            # 测试URL匹配
            if modules:
                test_module = modules[0]
                match_result = module_manager.match_module_for_url(self.test_url)
                print(f"✅ URL匹配测试: {match_result}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模组管理测试失败: {e}")
            return False
    
    def test_ai_functionality(self) -> bool:
        """测试AI功能"""
        print("\n🤖 测试AI功能")
        print("="*50)
        
        try:
            from ai.helper import EnhancedAIConfigManager
            from ai.analyzer import AIAnalyzerWithTesting
            
            # 测试AI配置管理器
            ai_manager = EnhancedAIConfigManager()
            print("✅ AI配置管理器创建成功")
            
            # 测试AI分析器
            analyzer = AIAnalyzerWithTesting()
            print("✅ AI分析器创建成功")
            
            # 简单的分析测试（不实际调用API）
            print("✅ AI功能模块加载正常")
            
            return True
            
        except Exception as e:
            print(f"❌ AI功能测试失败: {e}")
            return False
    
    def test_core_crawler(self) -> bool:
        """测试核心爬虫功能"""
        print("\n🕷️ 测试核心爬虫功能")
        print("="*50)
        
        try:
            from core import crawler
            from core.failed_url_processor import FailedUrlProcessor
            from core.PaginationHandler import PaginationHandler
            
            print("✅ 核心爬虫模块导入成功")
            
            # 测试失败URL处理器
            failed_processor = FailedUrlProcessor()
            print("✅ 失败URL处理器创建成功")
            
            # 测试分页处理器（需要page参数，这里只测试类导入）
            print("✅ 分页处理器类导入成功")
            
            # 测试工具函数
            full_link = crawler.get_full_link("/test", "http://example.com", "http://example.com", "absolute")
            print(f"✅ 链接处理测试: {full_link}")
            
            return True
            
        except Exception as e:
            print(f"❌ 核心爬虫测试失败: {e}")
            return False
    
    def test_gui_components(self) -> bool:
        """测试GUI组件"""
        print("\n🖥️ 测试GUI组件")
        print("="*50)
        
        try:
            from gui.config_manager import GUIConfigManager
            from gui.utils import ConfigValidator
            
            # 测试GUI配置管理器
            gui_config = GUIConfigManager()
            print("✅ GUI配置管理器创建成功")
            
            # 测试配置验证器
            validator = ConfigValidator()
            print("✅ 配置验证器创建成功")
            
            # 注意：不测试实际GUI窗口，避免弹出界面
            print("✅ GUI组件模块加载正常")
            
            return True
            
        except Exception as e:
            print(f"❌ GUI组件测试失败: {e}")
            return False
    
    def test_testing_framework(self) -> bool:
        """测试测试框架"""
        print("\n🧪 测试测试框架")
        print("="*50)
        
        try:
            from testing.selectors_test import SelectorsTestManager
            
            # 测试选择器测试管理器
            test_manager = SelectorsTestManager()
            print("✅ 选择器测试管理器创建成功")
            
            # 测试配置验证（简化版本）
            test_config = {
                "name": "测试配置",
                "url": self.test_url,
                "selectors": {
                    "container": ".list",
                    "item": ".item"
                }
            }

            # 简单的配置验证逻辑
            required_fields = ['name', 'url', 'selectors']
            is_valid = all(field in test_config for field in required_fields)
            print(f"✅ 配置验证测试: {'通过' if is_valid else '失败'}")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试框架测试失败: {e}")
            return False
    
    def test_utils_functions(self) -> bool:
        """测试工具函数"""
        print("\n🔧 测试工具函数")
        print("="*50)
        
        try:
            from utils.text_cleaner import clean_text, normalize_date
            
            # 测试文本清理
            test_text = "<p>测试文本</p>"
            cleaned = clean_text(test_text)
            print(f"✅ 文本清理测试: '{test_text}' -> '{cleaned}'")
            
            # 测试日期标准化
            test_date = "2024年1月1日"
            normalized = normalize_date(test_date)
            print(f"✅ 日期标准化测试: '{test_date}' -> '{normalized}'")
            
            return True
            
        except Exception as e:
            print(f"❌ 工具函数测试失败: {e}")
            return False
    
    def test_import_compatibility(self) -> bool:
        """测试导入兼容性"""
        print("\n🔗 测试导入兼容性")
        print("="*50)
        
        try:
            # 测试新的导入方式
            from core import crawler
            from gui import main_window
            from ai import analyzer
            from modules import manager
            print("✅ 新导入方式正常")
            
            # 测试兼容性导入
            import crawler as old_crawler
            import module_manager as old_module_manager
            print("✅ 兼容性导入正常")
            
            return True
            
        except Exception as e:
            print(f"❌ 导入兼容性测试失败: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🧪 开始全功能测试")
        print("="*80)
        
        tests = [
            ("配置管理", self.test_config_management),
            ("模组管理", self.test_module_management),
            ("AI功能", self.test_ai_functionality),
            ("核心爬虫", self.test_core_crawler),
            ("GUI组件", self.test_gui_components),
            ("测试框架", self.test_testing_framework),
            ("工具函数", self.test_utils_functions),
            ("导入兼容性", self.test_import_compatibility)
        ]
        
        results = {}
        passed = 0
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
                results[test_name] = False
        
        # 汇总结果
        print("\n" + "="*80)
        print("🎯 测试结果汇总")
        print("="*80)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总计: {passed}/{len(tests)} 测试通过")
        
        if passed == len(tests):
            print("\n🎉 所有功能测试通过！系统运行正常！")
        else:
            print(f"\n⚠️ 有 {len(tests) - passed} 个功能需要修复")
        
        return results

def main():
    """主函数"""
    tester = FunctionTester()
    results = tester.run_all_tests()
    
    # 返回退出码
    all_passed = all(results.values())
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    main()
