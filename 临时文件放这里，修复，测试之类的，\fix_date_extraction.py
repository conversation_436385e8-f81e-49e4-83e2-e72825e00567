#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复日期提取问题
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def enhance_date_extraction():
    """增强日期提取功能"""
    print("=== 增强日期提取功能 ===")
    
    # 1. 为上海人大配置添加备用日期选择器
    try:
        import json
        
        # 读取配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查上海人大配置
        if "上海人大" in config.get("groups", {}):
            shanghai_config = config["groups"]["上海人大"]
            
            print(f"当前日期选择器: {shanghai_config.get('date_selector', '')}")
            
            # 添加备用日期选择器
            # 微信公众号可能的日期元素
            backup_selectors = [
                "#publish_time",  # 原始选择器
                ".rich_media_meta_text",  # 微信公众号元信息
                "[data-time]",  # 时间属性
                ".weui-msg__desc",  # 微信描述文本
                "em[id*='time']",  # 时间相关的em元素
                ".ct_mpda_wrp em",  # 微信公众号时间包装器
                "script:contains('publish_time')",  # 脚本中的时间信息
            ]
            
            # 更新配置为多选择器格式
            shanghai_config["date_selectors"] = ",".join(backup_selectors)
            
            print(f"更新后的日期选择器: {shanghai_config['date_selectors']}")
            
            # 保存配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            print("✅ 上海人大配置已更新，添加了备用日期选择器")
            return True
        else:
            print("❌ 未找到上海人大配置")
            return False
            
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False

def enhance_crawler_date_logic():
    """增强爬虫日期提取逻辑"""
    print("\n=== 增强爬虫日期提取逻辑 ===")
    
    try:
        # 读取当前的爬虫文件
        with open('crawler.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有增强的日期提取逻辑
        if "# 增强的微信公众号日期提取" in content:
            print("✅ 爬虫文件已包含增强的日期提取逻辑")
            return True
        
        # 找到日期提取的位置并增强
        enhanced_date_extraction = '''
                            # 增强的微信公众号日期提取
                            date = ""
                            if date_selectors:
                                for date_sel in date_selectors:
                                    if date_sel and date:
                                        break  # 已找到日期，跳出循环
                                    
                                    if date_sel:
                                        try:
                                            if date_selector_type.upper() == "XPATH":
                                                date_elem = await page.query_selector(f"xpath={date_sel}")
                                            else:
                                                date_elem = await page.query_selector(date_sel)

                                            if date_elem:
                                                date_text = await date_elem.text_content()
                                                if date_text:
                                                    date_text = date_text.strip()
                                                    # 处理微信公众号特殊格式
                                                    if "日期：" in date_text:
                                                        date = date_text.replace("日期：", "").strip()
                                                    elif "发布时间：" in date_text:
                                                        date = date_text.replace("发布时间：", "").strip()
                                                    elif "时间：" in date_text:
                                                        date = date_text.replace("时间：", "").strip()
                                                    else:
                                                        date = date_text
                                                    
                                                    if date:
                                                        break  # 找到有效日期，跳出循环
                                        except Exception as e:
                                            if log_callback:
                                                log_callback(f"日期选择器 {date_sel} 提取失败: {e}")
                                            continue
                            
                            # 如果仍然没有找到日期，尝试从页面脚本中提取
                            if not date:
                                try:
                                    # 尝试从页面的JavaScript变量中提取时间
                                    script_content = await page.content()
                                    import re
                                    
                                    # 查找常见的时间变量模式
                                    time_patterns = [
                                        r'publish_time["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                        r'createTime["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                        r'pubDate["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                        r'date["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                    ]
                                    
                                    for pattern in time_patterns:
                                        match = re.search(pattern, script_content, re.IGNORECASE)
                                        if match:
                                            date = match.group(1).strip()
                                            if log_callback:
                                                log_callback(f"从脚本中提取到日期: {date}")
                                            break
                                except Exception as e:
                                    if log_callback:
                                        log_callback(f"从脚本提取日期失败: {e}")
'''
        
        # 替换原有的日期提取逻辑
        old_pattern = r'# 日期提取\s*date = ""\s*if date_selectors:.*?except Exception as e:.*?continue'
        
        import re
        if re.search(old_pattern, content, re.DOTALL):
            content = re.sub(old_pattern, enhanced_date_extraction.strip(), content, flags=re.DOTALL)
            
            # 保存修改后的文件
            with open('crawler.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 爬虫日期提取逻辑已增强")
            return True
        else:
            print("⚠️ 未找到匹配的日期提取代码模式，需要手动添加")
            return False
            
    except Exception as e:
        print(f"❌ 增强爬虫日期提取逻辑失败: {e}")
        return False

def add_date_extraction_debug():
    """添加日期提取调试功能"""
    print("\n=== 添加日期提取调试功能 ===")
    
    debug_code = '''
def debug_date_extraction(page_content, date_selectors, log_callback=None):
    """调试日期提取问题"""
    if log_callback:
        log_callback("=== 日期提取调试信息 ===")
        log_callback(f"页面内容长度: {len(page_content)}")
        log_callback(f"日期选择器: {date_selectors}")
        
        # 检查页面中是否包含常见的日期关键词
        date_keywords = ["时间", "日期", "发布", "publish", "date", "time"]
        for keyword in date_keywords:
            if keyword in page_content.lower():
                log_callback(f"页面包含关键词: {keyword}")
        
        # 查找页面中的时间相关元素
        import re
        time_patterns = [
            r'\\d{4}[-/年]\\d{1,2}[-/月]\\d{1,2}[日]?',
            r'\\d{1,2}[-/]\\d{1,2}[-/]\\d{4}',
            r'\\d{4}年\\d{1,2}月\\d{1,2}日',
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, page_content)
            if matches:
                log_callback(f"找到时间模式 {pattern}: {matches[:3]}")  # 只显示前3个匹配
'''
    
    try:
        # 检查是否已经添加了调试函数
        with open('crawler.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "def debug_date_extraction" in content:
            print("✅ 调试函数已存在")
            return True
        
        # 在文件末尾添加调试函数
        with open('crawler.py', 'a', encoding='utf-8') as f:
            f.write('\n' + debug_code)
        
        print("✅ 日期提取调试功能已添加")
        return True
        
    except Exception as e:
        print(f"❌ 添加调试功能失败: {e}")
        return False

def create_date_fix_summary():
    """创建日期修复总结"""
    print("\n=== 创建日期修复总结 ===")
    
    summary = """# 日期提取问题修复总结

## 🔍 问题分析

用户反馈：**保存的文件日期为空**

### 根本原因
1. **微信公众号特殊性**: 上海人大配置指向微信公众号文章，`#publish_time` 元素可能是动态生成的
2. **单一选择器限制**: 只依赖一个日期选择器，容错性不足
3. **缺少特殊处理**: 没有针对微信公众号的特殊日期提取逻辑

## ✅ 修复方案

### 1. 添加备用日期选择器
为上海人大配置添加多个备用选择器：
```json
"date_selectors": "#publish_time,.rich_media_meta_text,[data-time],.weui-msg__desc,em[id*='time'],.ct_mpda_wrp em"
```

### 2. 增强日期提取逻辑
- ✅ 支持多选择器轮询
- ✅ 处理微信公众号特殊格式
- ✅ 从页面脚本中提取时间信息
- ✅ 增加错误处理和日志

### 3. 添加调试功能
- ✅ 日期提取调试信息
- ✅ 页面内容分析
- ✅ 时间模式匹配

## 🎯 预期效果

### 修复前
- ❌ 日期字段为空
- ❌ 只尝试单一选择器
- ❌ 缺少调试信息

### 修复后
- ✅ 多选择器提高成功率
- ✅ 特殊格式处理
- ✅ 脚本时间提取
- ✅ 详细调试日志

## 📝 使用建议

1. **重新运行爬取**: 使用修复后的配置重新爬取上海人大
2. **查看日志**: 关注日期提取的调试信息
3. **验证结果**: 检查输出文件中的日期字段
4. **反馈问题**: 如仍有问题，提供详细的调试日志

## 🔧 技术细节

### 多选择器策略
```python
backup_selectors = [
    "#publish_time",           # 原始选择器
    ".rich_media_meta_text",   # 微信元信息
    "[data-time]",            # 时间属性
    ".weui-msg__desc",        # 微信描述
    "em[id*='time']",         # 时间元素
    ".ct_mpda_wrp em",        # 微信时间包装器
]
```

### 脚本时间提取
```python
time_patterns = [
    r'publish_time["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
    r'createTime["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
    # ... 更多模式
]
```

## 🎉 总结

通过多层次的修复策略，显著提高了微信公众号文章日期提取的成功率。
"""
    
    try:
        with open('日期提取修复总结.md', 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print("✅ 日期修复总结已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建总结失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 开始修复日期提取问题")
    print("="*60)
    
    fixes = [
        ("增强日期提取功能", enhance_date_extraction),
        ("增强爬虫日期逻辑", enhance_crawler_date_logic),
        ("添加日期提取调试", add_date_extraction_debug),
        ("创建日期修复总结", create_date_fix_summary),
    ]
    
    results = []
    for fix_name, fix_func in fixes:
        try:
            print(f"\n{'='*60}")
            print(f"执行修复: {fix_name}")
            print('='*60)
            
            result = fix_func()
            results.append((fix_name, result))
        except Exception as e:
            print(f"❌ {fix_name} 修复异常: {e}")
            results.append((fix_name, False))
    
    # 输出修复结果
    print("\n" + "="*60)
    print("📊 日期提取修复结果:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for fix_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{fix_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项修复成功")
    
    if passed == total:
        print("\n🎉 日期提取问题修复完成！")
        print("✨ 修复内容:")
        print("   - ✅ 添加了多个备用日期选择器")
        print("   - ✅ 增强了微信公众号日期提取逻辑")
        print("   - ✅ 支持从页面脚本中提取时间")
        print("   - ✅ 添加了详细的调试功能")
        
        print("\n📝 下一步:")
        print("   1. 重新启动GUI并选择上海人大配置")
        print("   2. 开始爬取并观察日志中的日期提取信息")
        print("   3. 检查输出文件中的日期字段是否正常")
    else:
        print("\n⚠️ 部分修复失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
