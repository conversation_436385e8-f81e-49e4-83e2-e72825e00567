#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模组配置读取
"""

def test_module_config_loading():
    """测试模组配置加载"""
    print("🔧 测试模组配置读取")
    print("="*50)
    
    try:
        from modules.manager import module_manager
        
        print(f"配置文件路径: {module_manager.config_file}")
        print(f"已加载模组数量: {len(module_manager.modules)}")
        print(f"匹配优先级: {module_manager.match_priority}")
        
        print("\n📋 模组详情:")
        for name, config in module_manager.modules.items():
            print(f"\n模组: {name}")
            print(f"  描述: {config.get('description', '无描述')}")
            print(f"  域名模式: {config.get('domain_patterns', [])}")
            print(f"  URL模式: {config.get('url_patterns', [])}")
            
            module_config = config.get('config', {})
            print(f"  配置项数量: {len(module_config)}")
            
            # 显示关键配置
            key_configs = ['title_selectors', 'content_selectors', 'mode', 'file_format']
            for key in key_configs:
                if key in module_config:
                    print(f"    {key}: {module_config[key]}")
        
        # 测试URL匹配
        print("\n🔍 测试URL匹配:")
        test_urls = [
            "http://www.zhzx.gov.cn/zwhgz/jjwkjw/test.html",
            "https://mp.weixin.qq.com/s/test123",
            "https://example.com/test"
        ]
        
        for url in test_urls:
            matched = module_manager.match_url(url)
            print(f"  {url} -> {matched}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_file_content():
    """测试配置文件内容"""
    print("\n📄 测试配置文件内容")
    print("="*50)
    
    try:
        import json
        import os
        
        config_file = "configs/modules/module_configs.json"
        
        if not os.path.exists(config_file):
            print(f"❌ 配置文件不存在: {config_file}")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 配置文件读取成功: {config_file}")
        print(f"顶级键: {list(data.keys())}")
        
        for key, value in data.items():
            if key == 'match_priority':
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: {type(value)} - {value.get('description', '无描述') if isinstance(value, dict) else value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 模组配置测试")
    print("="*80)
    
    # 测试配置文件内容
    config_test = test_config_file_content()
    
    # 测试模组配置加载
    module_test = test_module_config_loading()
    
    print("\n" + "="*80)
    print("🎯 测试结果")
    print("="*80)
    
    if config_test and module_test:
        print("✅ 所有测试通过")
        print("✅ 配置文件读取正常")
        print("✅ 模组配置加载正常")
        print("✅ URL匹配功能正常")
    else:
        print("❌ 测试失败")
        if not config_test:
            print("  - 配置文件读取有问题")
        if not module_test:
            print("  - 模组配置加载有问题")
