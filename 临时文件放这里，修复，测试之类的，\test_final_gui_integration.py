#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终GUI集成测试
验证所有字段配置功能是否正确集成到GUI中
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gui_startup():
    """测试GUI启动"""
    print("=" * 60)
    print("测试GUI启动")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        print("🚀 正在创建主窗口...")
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查窗口基本属性
        window_title = window.windowTitle()
        print(f"📋 窗口标题: {window_title}")
        
        # 检查是否有错误
        if hasattr(window, 'log_text'):
            print("✅ 日志组件已创建")
        else:
            print("⚠️ 日志组件未创建")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_config_integration():
    """测试字段配置集成"""
    print("=" * 60)
    print("测试字段配置集成")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查字段配置相关组件
        field_components = [
            ('fields_tab', '字段配置标签页'),
            ('fields_sub_tab_widget', '字段配置子标签页控件'),
            ('field_selection_tab', '字段选择子标签页'),
            ('field_selectors_tab', '选择器编辑子标签页'),
            ('enable_field_config_checkbox', '字段配置开关'),
            ('enable_selector_edit_checkbox', '选择器编辑开关'),
            ('field_checkboxes', '字段复选框字典'),
            ('field_selector_editors', '字段选择器编辑器字典')
        ]
        
        component_count = 0
        for attr_name, display_name in field_components:
            has_component = hasattr(window, attr_name)
            print(f"🔧 {display_name}: {'✅' if has_component else '❌'}")
            if has_component:
                component_count += 1
        
        print(f"📊 字段配置组件完整性: {component_count}/{len(field_components)}")
        
        # 检查字段数量
        if hasattr(window, 'field_checkboxes'):
            checkbox_count = len(window.field_checkboxes)
            print(f"📝 字段复选框数量: {checkbox_count}")
        
        if hasattr(window, 'field_selector_editors'):
            editor_count = len(window.field_selector_editors)
            print(f"📝 选择器编辑器数量: {editor_count}")
        
        return component_count >= len(field_components) * 0.8
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_switch_functionality():
    """测试开关功能"""
    print("=" * 60)
    print("测试开关功能")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 测试字段配置开关
        if hasattr(window, 'enable_field_config_checkbox'):
            print("\n🔧 测试字段配置开关...")
            
            # 测试启用
            window.enable_field_config_checkbox.setChecked(True)
            field_config_enabled = window.enable_field_config_checkbox.isChecked()
            print(f"   启用状态: {'✅' if field_config_enabled else '❌'}")
            
            # 测试禁用
            window.enable_field_config_checkbox.setChecked(False)
            field_config_disabled = not window.enable_field_config_checkbox.isChecked()
            print(f"   禁用状态: {'✅' if field_config_disabled else '❌'}")
        else:
            print("❌ 字段配置开关不存在")
            return False
        
        # 测试选择器编辑开关
        if hasattr(window, 'enable_selector_edit_checkbox'):
            print("\n🔧 测试选择器编辑开关...")
            
            # 测试启用
            window.enable_selector_edit_checkbox.setChecked(True)
            selector_edit_enabled = window.enable_selector_edit_checkbox.isChecked()
            print(f"   启用状态: {'✅' if selector_edit_enabled else '❌'}")
            
            # 测试禁用
            window.enable_selector_edit_checkbox.setChecked(False)
            selector_edit_disabled = not window.enable_selector_edit_checkbox.isChecked()
            print(f"   禁用状态: {'✅' if selector_edit_disabled else '❌'}")
        else:
            print("❌ 选择器编辑开关不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_retrieval():
    """测试配置获取"""
    print("=" * 60)
    print("测试配置获取")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 测试字段配置获取
        if hasattr(window, 'get_field_config_from_gui'):
            print("\n🔧 测试字段配置获取...")
            
            # 测试禁用状态
            if hasattr(window, 'enable_field_config_checkbox'):
                window.enable_field_config_checkbox.setChecked(False)
            
            config_disabled = window.get_field_config_from_gui()
            print(f"   禁用状态配置: {config_disabled}")
            
            disabled_correct = (config_disabled.get('use_field_config') == False)
            print(f"   禁用状态正确: {'✅' if disabled_correct else '❌'}")
            
            # 测试启用状态
            if hasattr(window, 'enable_field_config_checkbox'):
                window.enable_field_config_checkbox.setChecked(True)
            
            config_enabled = window.get_field_config_from_gui()
            print(f"   启用状态配置: {config_enabled}")
            
            enabled_correct = (config_enabled.get('use_field_config') == True)
            print(f"   启用状态正确: {'✅' if enabled_correct else '❌'}")
            
            return disabled_correct and enabled_correct
        else:
            print("❌ 字段配置获取方法不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_workflow():
    """测试完整工作流程"""
    print("=" * 60)
    print("测试完整工作流程")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 模拟完整的配置流程
        print("\n🔄 模拟完整配置流程...")
        
        # 1. 启用字段配置
        if hasattr(window, 'enable_field_config_checkbox'):
            window.enable_field_config_checkbox.setChecked(True)
            print("   ✅ 已启用字段配置")
        
        # 2. 选择字段预设
        if hasattr(window, 'field_preset_combo'):
            if window.field_preset_combo.count() > 0:
                window.field_preset_combo.setCurrentIndex(1)  # 选择第二个预设
                print("   ✅ 已选择字段预设")
        
        # 3. 启用选择器编辑
        if hasattr(window, 'enable_selector_edit_checkbox'):
            window.enable_selector_edit_checkbox.setChecked(True)
            print("   ✅ 已启用选择器编辑")
        
        # 4. 获取最终配置
        if hasattr(window, 'get_field_config_from_gui'):
            final_config = window.get_field_config_from_gui()
            print(f"   📊 最终配置: {final_config}")
            
            config_valid = (
                final_config.get('use_field_config') == True and
                isinstance(final_config.get('field_preset'), str) and
                isinstance(final_config.get('custom_field_list'), list)
            )
            print(f"   配置有效性: {'✅' if config_valid else '❌'}")
            
            return config_valid
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终GUI集成测试")
    print()
    
    tests = [
        ("GUI启动测试", test_gui_startup),
        ("字段配置集成测试", test_field_config_integration),
        ("开关功能测试", test_switch_functionality),
        ("配置获取测试", test_config_retrieval),
        ("完整工作流程测试", test_complete_workflow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("最终测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI集成的字段配置功能完全正常。")
        print("\n🎯 功能总结:")
        print("   ✅ 字段配置开关功能")
        print("   ✅ 字段预设选择功能")
        print("   ✅ 自定义字段选择功能")
        print("   ✅ 选择器编辑开关功能")
        print("   ✅ 集成选择器编辑功能")
        print("   ✅ 配置获取和传递功能")
        print("\n💡 用户可以通过GUI完整地配置字段和选择器，无需任何编程知识！")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
