#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段配置覆盖功能 V2
验证参考模组配置逻辑的字段配置覆盖是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_field_config_override():
    """测试字段配置覆盖功能"""
    print("=" * 60)
    print("测试字段配置覆盖功能 V2")
    print("=" * 60)
    
    try:
        # 导入相关模块
        from core.field_config_manager import get_field_config_manager
        
        print("✅ 模块导入成功")
        
        # 获取字段配置管理器
        manager = get_field_config_manager()
        all_fields = manager.get_available_fields()
        print(f"📊 可用字段数量: {len(all_fields)}")
        
        # 测试预设配置
        presets = manager.get_field_presets()
        print(f"📋 可用预设: {list(presets.keys())}")
        
        if 'social_media' in presets:
            print(f"\n🧪 测试社交媒体预设")
            
            # 模拟字段配置处理逻辑
            field_preset = 'social_media'
            field_configs = {}
            
            # 获取预设字段配置
            field_names = presets[field_preset]
            print(f"📝 预设字段: {field_names}")
            
            for field_name in field_names:
                if field_name in all_fields:
                    field_configs[field_name] = all_fields[field_name]
            
            print(f"🔧 字段配置数量: {len(field_configs)}")
            
            # 模拟选择器覆盖逻辑
            title_selectors = ['.default-title']  # 默认选择器
            content_selectors = ['.default-content']
            date_selectors = ['.default-date']
            source_selectors = ['.default-source']
            
            print(f"\n📋 覆盖前的选择器:")
            print(f"   title: {title_selectors}")
            print(f"   content: {content_selectors}")
            print(f"   date: {date_selectors}")
            print(f"   source: {source_selectors}")
            
            # 应用字段配置覆盖 - 参考模组配置的做法
            if field_configs:
                print(f"\n🔧 应用字段配置覆盖:")
                
                # 只有字段配置中有相同字段时才覆盖，否则使用基础配置（传入参数）
                if 'title' in field_configs and 'selectors' in field_configs['title']:
                    title_selectors = field_configs['title']['selectors']
                    print(f"   📝 title: {len(title_selectors)} 个选择器")
                        
                if 'content' in field_configs and 'selectors' in field_configs['content']:
                    content_selectors = field_configs['content']['selectors']
                    print(f"   📝 content: {len(content_selectors)} 个选择器")
                        
                if 'dateget' in field_configs and 'selectors' in field_configs['dateget']:
                    date_selectors = field_configs['dateget']['selectors']
                    print(f"   📝 dateget: {len(date_selectors)} 个选择器")
                        
                if 'source' in field_configs and 'selectors' in field_configs['source']:
                    source_selectors = field_configs['source']['selectors']
                    print(f"   📝 source: {len(source_selectors)} 个选择器")
            
            print(f"\n📋 覆盖后的选择器:")
            print(f"   title: {title_selectors}")
            print(f"   content: {content_selectors}")
            print(f"   date: {date_selectors}")
            print(f"   source: {source_selectors}")
            
            # 验证覆盖效果
            title_overridden = title_selectors != ['.default-title']
            content_overridden = content_selectors != ['.default-content']
            date_overridden = date_selectors != ['.default-date']
            source_overridden = source_selectors != ['.default-source']
            
            print(f"\n✅ 覆盖效果验证:")
            print(f"   title覆盖: {'✅ 成功' if title_overridden else '❌ 失败'}")
            print(f"   content覆盖: {'✅ 成功' if content_overridden else '❌ 失败'}")
            print(f"   date覆盖: {'✅ 成功' if date_overridden else '❌ 失败'}")
            print(f"   source覆盖: {'✅ 成功' if source_overridden else '❌ 失败'}")
            
            success_count = sum([title_overridden, content_overridden, date_overridden, source_overridden])
            print(f"\n📊 覆盖成功率: {success_count}/4 ({success_count*25}%)")
            
            return success_count >= 2  # 至少一半字段成功覆盖
        else:
            print("❌ 社交媒体预设不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_field_config():
    """测试自定义字段配置"""
    print("=" * 60)
    print("测试自定义字段配置")
    print("=" * 60)
    
    try:
        from core.field_config_manager import get_field_config_manager
        
        # 获取字段配置管理器
        manager = get_field_config_manager()
        all_fields = manager.get_available_fields()
        
        # 测试自定义字段列表
        custom_field_list = ['title', 'content', 'likes', 'views']
        print(f"📝 自定义字段列表: {custom_field_list}")
        
        # 模拟自定义字段配置处理逻辑
        field_configs = {}
        
        for field_name in custom_field_list:
            if field_name in all_fields:
                field_configs[field_name] = all_fields[field_name]
        
        print(f"🔧 字段配置数量: {len(field_configs)}")
        
        # 模拟选择器覆盖逻辑
        title_selectors = ['.default-title']
        content_selectors = ['.default-content']
        likes_selectors = ['.default-likes']  # 扩展字段
        
        print(f"\n📋 覆盖前的选择器:")
        print(f"   title: {title_selectors}")
        print(f"   content: {content_selectors}")
        print(f"   likes: {likes_selectors}")
        
        # 应用字段配置覆盖
        if field_configs:
            print(f"\n🔧 应用自定义字段配置覆盖:")
            
            if 'title' in field_configs and 'selectors' in field_configs['title']:
                title_selectors = field_configs['title']['selectors']
                print(f"   📝 title: {len(title_selectors)} 个选择器")
                    
            if 'content' in field_configs and 'selectors' in field_configs['content']:
                content_selectors = field_configs['content']['selectors']
                print(f"   📝 content: {len(content_selectors)} 个选择器")
                    
            if 'likes' in field_configs and 'selectors' in field_configs['likes']:
                likes_selectors = field_configs['likes']['selectors']
                print(f"   📝 likes: {len(likes_selectors)} 个选择器")
        
        print(f"\n📋 覆盖后的选择器:")
        print(f"   title: {title_selectors}")
        print(f"   content: {content_selectors}")
        print(f"   likes: {likes_selectors}")
        
        # 验证覆盖效果
        title_overridden = title_selectors != ['.default-title']
        content_overridden = content_selectors != ['.default-content']
        likes_overridden = likes_selectors != ['.default-likes']
        
        print(f"\n✅ 自定义字段覆盖效果验证:")
        print(f"   title覆盖: {'✅ 成功' if title_overridden else '❌ 失败'}")
        print(f"   content覆盖: {'✅ 成功' if content_overridden else '❌ 失败'}")
        print(f"   likes覆盖: {'✅ 成功' if likes_overridden else '❌ 失败'}")
        
        success_count = sum([title_overridden, content_overridden, likes_overridden])
        print(f"\n📊 自定义字段覆盖成功率: {success_count}/3 ({success_count*33:.0f}%)")
        
        return success_count >= 2
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试字段配置覆盖功能 V2")
    print("📝 使用参考模组配置的逻辑")
    print()
    
    tests = [
        ("字段预设覆盖测试", test_field_config_override),
        ("自定义字段覆盖测试", test_custom_field_config),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！字段配置覆盖功能正常。")
        print("\n💡 功能说明:")
        print("   ✅ 参考模组配置逻辑实现字段配置覆盖")
        print("   ✅ 支持字段预设和自定义字段列表")
        print("   ✅ 直接覆盖选择器变量，无需全局状态")
        print("   ✅ 只有字段配置中有相同字段时才覆盖")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
