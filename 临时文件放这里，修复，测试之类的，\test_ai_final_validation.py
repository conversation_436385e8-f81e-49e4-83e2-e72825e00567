#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块最终验证测试
全面验证AI模块的所有功能和修复
"""

import sys
import os
import asyncio
import traceback
import json

def test_configuration_system():
    """测试配置系统"""
    print("🔧 测试配置系统...")
    print("="*50)
    
    results = {}
    
    # 测试AI配置加载
    try:
        from ai.analyzer import load_ai_config, get_openai_client
        
        config = load_ai_config()
        if isinstance(config, dict) and 'api_key' in config:
            results['config_loading'] = "✅ 成功"
            print("✅ AI配置加载成功")
        else:
            results['config_loading'] = "❌ 配置格式错误"
            print("❌ AI配置格式错误")
        
        # 测试客户端获取
        client = get_openai_client()
        if client is not None or not config.get('enable_ai', True):
            results['client_creation'] = "✅ 成功"
            print("✅ OpenAI客户端创建成功")
        else:
            results['client_creation'] = "❌ 客户端创建失败"
            print("❌ OpenAI客户端创建失败")
        
    except Exception as e:
        results['configuration_test'] = f"❌ 失败: {e}"
        print(f"❌ 配置系统测试失败: {e}")
    
    return results

def test_dependency_handling():
    """测试依赖处理"""
    print("\n🔗 测试依赖处理...")
    print("="*50)
    
    results = {}
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting, CRAWLER_AVAILABLE, SELECTORS_TEST_AVAILABLE, FIELD_CONFIG_AVAILABLE
        
        print(f"   Crawler可用: {CRAWLER_AVAILABLE}")
        print(f"   选择器测试可用: {SELECTORS_TEST_AVAILABLE}")
        print(f"   字段配置可用: {FIELD_CONFIG_AVAILABLE}")
        
        # 测试分析器创建（即使依赖不可用也应该成功）
        analyzer = AIAnalyzerWithTesting()
        results['analyzer_creation'] = "✅ 成功"
        print("✅ AI分析器创建成功（依赖处理正确）")
        
        # 检查依赖状态
        if hasattr(analyzer, 'test_manager'):
            if SELECTORS_TEST_AVAILABLE and analyzer.test_manager is not None:
                results['test_manager_init'] = "✅ 成功"
                print("✅ 测试管理器初始化成功")
            elif not SELECTORS_TEST_AVAILABLE and analyzer.test_manager is None:
                results['test_manager_init'] = "✅ 正确处理不可用"
                print("✅ 测试管理器正确处理不可用状态")
            else:
                results['test_manager_init'] = "❌ 状态不一致"
                print("❌ 测试管理器状态不一致")
        
        if hasattr(analyzer, 'field_config_manager'):
            if FIELD_CONFIG_AVAILABLE and analyzer.field_config_manager is not None:
                results['field_config_init'] = "✅ 成功"
                print("✅ 字段配置管理器初始化成功")
            elif not FIELD_CONFIG_AVAILABLE and analyzer.field_config_manager is None:
                results['field_config_init'] = "✅ 正确处理不可用"
                print("✅ 字段配置管理器正确处理不可用状态")
            else:
                results['field_config_init'] = "❌ 状态不一致"
                print("❌ 字段配置管理器状态不一致")
        
    except Exception as e:
        results['dependency_handling'] = f"❌ 失败: {e}"
        print(f"❌ 依赖处理测试失败: {e}")
        traceback.print_exc()
    
    return results

def test_field_integration():
    """测试字段集成"""
    print("\n📋 测试字段集成...")
    print("="*50)
    
    results = {}
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        
        # 测试字段选择器获取
        selectors = analyzer.get_field_selectors_for_analysis()
        if isinstance(selectors, dict) and len(selectors) > 0:
            results['field_selectors'] = "✅ 成功"
            print(f"✅ 字段选择器获取成功，包含 {len(selectors)} 个字段类型")
        else:
            results['field_selectors'] = "❌ 返回为空或格式错误"
            print("❌ 字段选择器获取失败")
        
        # 测试预设字段选择器
        preset_selectors = analyzer.get_field_selectors_for_analysis(field_preset="basic")
        if isinstance(preset_selectors, dict):
            results['preset_selectors'] = "✅ 成功"
            print("✅ 预设字段选择器获取成功")
        else:
            results['preset_selectors'] = "❌ 预设选择器获取失败"
            print("❌ 预设字段选择器获取失败")
        
        # 测试自定义字段选择器
        custom_selectors = analyzer.get_field_selectors_for_analysis(custom_fields=["title", "content"])
        if isinstance(custom_selectors, dict):
            results['custom_selectors'] = "✅ 成功"
            print("✅ 自定义字段选择器获取成功")
        else:
            results['custom_selectors'] = "❌ 自定义选择器获取失败"
            print("❌ 自定义字段选择器获取失败")
        
    except Exception as e:
        results['field_integration'] = f"❌ 失败: {e}"
        print(f"❌ 字段集成测试失败: {e}")
        traceback.print_exc()
    
    return results

def test_ai_helper_integration():
    """测试AI助手集成"""
    print("\n🤖 测试AI助手集成...")
    print("="*50)
    
    results = {}
    
    try:
        from ai.helper import EnhancedAIConfigManager, AIAnalysisThread
        
        # 测试管理器创建
        manager = EnhancedAIConfigManager()
        results['manager_creation'] = "✅ 成功"
        print("✅ AI配置管理器创建成功")
        
        # 测试关键方法
        methods = ['check_llm_config', 'is_running', 'start_ai_analysis_new']
        for method in methods:
            if hasattr(manager, method):
                results[f'method_{method}'] = "✅ 存在"
                print(f"✅ 方法 {method} 存在")
            else:
                results[f'method_{method}'] = "❌ 缺失"
                print(f"❌ 方法 {method} 缺失")
        
        # 测试线程类
        try:
            thread = AIAnalysisThread("http://example.com", "full", "basic", ["title", "content"])
            if hasattr(thread, 'field_preset') and hasattr(thread, 'custom_fields'):
                results['thread_field_support'] = "✅ 成功"
                print("✅ AI分析线程支持字段配置")
            else:
                results['thread_field_support'] = "❌ 缺少字段支持"
                print("❌ AI分析线程缺少字段配置支持")
        except Exception as e:
            results['thread_creation'] = f"❌ 失败: {e}"
            print(f"❌ AI分析线程创建失败: {e}")
        
    except Exception as e:
        results['ai_helper_integration'] = f"❌ 失败: {e}"
        print(f"❌ AI助手集成测试失败: {e}")
        traceback.print_exc()
    
    return results

def test_error_handling():
    """测试错误处理"""
    print("\n⚠️ 测试错误处理...")
    print("="*50)
    
    results = {}
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        
        # 测试无效字段预设处理
        try:
            selectors = analyzer.get_field_selectors_for_analysis(field_preset="invalid_preset")
            if isinstance(selectors, dict) and len(selectors) > 0:
                results['invalid_preset_handling'] = "✅ 成功"
                print("✅ 无效预设处理正确（返回默认选择器）")
            else:
                results['invalid_preset_handling'] = "❌ 处理不当"
                print("❌ 无效预设处理不当")
        except Exception as e:
            results['invalid_preset_handling'] = f"❌ 异常: {e}"
            print(f"❌ 无效预设处理异常: {e}")
        
        # 测试空字段列表处理
        try:
            selectors = analyzer.get_field_selectors_for_analysis(custom_fields=[])
            if isinstance(selectors, dict) and len(selectors) > 0:
                results['empty_fields_handling'] = "✅ 成功"
                print("✅ 空字段列表处理正确")
            else:
                results['empty_fields_handling'] = "❌ 处理不当"
                print("❌ 空字段列表处理不当")
        except Exception as e:
            results['empty_fields_handling'] = f"❌ 异常: {e}"
            print(f"❌ 空字段列表处理异常: {e}")
        
    except Exception as e:
        results['error_handling'] = f"❌ 失败: {e}"
        print(f"❌ 错误处理测试失败: {e}")
    
    return results

def generate_final_report(all_results):
    """生成最终报告"""
    print("\n📊 AI模块修复最终报告")
    print("="*70)
    
    total_tests = 0
    passed_tests = 0
    issues = []
    
    for category, results in all_results.items():
        print(f"\n{category}:")
        if isinstance(results, dict):
            for test_name, result in results.items():
                total_tests += 1
                if isinstance(result, str):
                    if result.startswith("✅"):
                        passed_tests += 1
                        status = "✅ 通过"
                    else:
                        status = "❌ 失败"
                        issues.append(f"{category}.{test_name}: {result}")
                else:
                    status = "❓ 未知"
                print(f"  {test_name}: {status}")
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n📈 总体统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    print(f"\n🎯 修复状态评估:")
    if success_rate >= 95:
        print("🎉 优秀！AI模块修复非常成功")
        status = "优秀"
    elif success_rate >= 85:
        print("✅ 良好！AI模块修复基本成功")
        status = "良好"
    elif success_rate >= 70:
        print("⚠️ 一般！AI模块修复部分成功，仍有改进空间")
        status = "一般"
    else:
        print("❌ 失败！AI模块修复需要进一步工作")
        status = "失败"
    
    if issues:
        print(f"\n🔍 需要关注的问题:")
        for issue in issues[:5]:  # 只显示前5个问题
            print(f"   - {issue}")
        if len(issues) > 5:
            print(f"   ... 还有 {len(issues) - 5} 个问题")
    
    # 保存报告
    report_data = {
        'timestamp': __import__('time').strftime('%Y-%m-%d %H:%M:%S'),
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'status': status,
        'results': all_results,
        'issues': issues
    }
    
    try:
        with open('ai_module_final_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        print(f"\n📄 详细报告已保存到: ai_module_final_report.json")
    except Exception as e:
        print(f"\n❌ 保存报告失败: {e}")
    
    return report_data

def main():
    """主函数"""
    print("🚀 AI模块最终验证测试")
    print("="*70)
    print("验证所有AI模块修复和集成...")
    
    all_results = {}
    
    # 运行所有测试
    all_results['配置系统'] = test_configuration_system()
    all_results['依赖处理'] = test_dependency_handling()
    all_results['字段集成'] = test_field_integration()
    all_results['AI助手集成'] = test_ai_helper_integration()
    all_results['错误处理'] = test_error_handling()
    
    # 生成最终报告
    report = generate_final_report(all_results)
    
    return report

if __name__ == "__main__":
    main()
