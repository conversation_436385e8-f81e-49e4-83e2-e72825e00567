#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块集成测试
验证AI模块与字段配置系统的集成是否正确
"""

import sys
import os
import asyncio
import traceback

def test_ai_field_integration():
    """测试AI模块与字段配置的集成"""
    print("🔍 测试AI模块与字段配置集成...")
    print("="*60)
    
    results = {}
    
    # 测试字段配置管理器导入
    try:
        from core.field_config_manager import FieldConfigManager
        results['field_config_manager'] = "✅ 成功"
        print("✅ 字段配置管理器导入成功")
    except Exception as e:
        results['field_config_manager'] = f"❌ 失败: {e}"
        print(f"❌ 字段配置管理器导入失败: {e}")
        return results
    
    # 测试AI分析器导入
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        results['ai_analyzer'] = "✅ 成功"
        print("✅ AI分析器导入成功")
    except Exception as e:
        results['ai_analyzer'] = f"❌ 失败: {e}"
        print(f"❌ AI分析器导入失败: {e}")
        return results
    
    # 测试AI分析器创建和字段配置集成
    try:
        analyzer = AIAnalyzerWithTesting()
        
        # 检查字段配置管理器是否正确初始化
        if hasattr(analyzer, 'field_config_manager'):
            if analyzer.field_config_manager is not None:
                results['field_config_integration'] = "✅ 成功"
                print("✅ AI分析器字段配置集成成功")
            else:
                results['field_config_integration'] = "⚠️ 字段配置管理器为None"
                print("⚠️ AI分析器字段配置管理器为None")
        else:
            results['field_config_integration'] = "❌ 缺少字段配置管理器属性"
            print("❌ AI分析器缺少字段配置管理器属性")
        
    except Exception as e:
        results['field_config_integration'] = f"❌ 失败: {e}"
        print(f"❌ AI分析器字段配置集成失败: {e}")
    
    # 测试字段选择器获取功能
    try:
        analyzer = AIAnalyzerWithTesting()
        
        # 测试默认选择器获取
        default_selectors = analyzer.get_field_selectors_for_analysis()
        if isinstance(default_selectors, dict) and 'title_selectors' in default_selectors:
            results['field_selectors_default'] = "✅ 成功"
            print("✅ 默认字段选择器获取成功")
        else:
            results['field_selectors_default'] = "❌ 返回格式错误"
            print("❌ 默认字段选择器获取失败")
        
        # 测试预设选择器获取
        preset_selectors = analyzer.get_field_selectors_for_analysis(field_preset="basic")
        if isinstance(preset_selectors, dict):
            results['field_selectors_preset'] = "✅ 成功"
            print("✅ 预设字段选择器获取成功")
        else:
            results['field_selectors_preset'] = "❌ 返回格式错误"
            print("❌ 预设字段选择器获取失败")
        
    except Exception as e:
        results['field_selectors_test'] = f"❌ 失败: {e}"
        print(f"❌ 字段选择器测试失败: {e}")
    
    # 测试AI助手集成
    try:
        from ai.helper import EnhancedAIConfigManager
        manager = EnhancedAIConfigManager()
        
        # 检查新的分析方法是否存在
        if hasattr(manager, 'start_ai_analysis_new'):
            results['ai_helper_integration'] = "✅ 成功"
            print("✅ AI助手字段配置集成成功")
        else:
            results['ai_helper_integration'] = "❌ 缺少新分析方法"
            print("❌ AI助手缺少字段配置支持方法")
        
    except Exception as e:
        results['ai_helper_integration'] = f"❌ 失败: {e}"
        print(f"❌ AI助手集成测试失败: {e}")
    
    return results

def test_field_presets():
    """测试字段预设功能"""
    print("\n📋 测试字段预设功能...")
    print("="*60)
    
    results = {}
    
    try:
        from core.field_config_manager import FieldConfigManager
        manager = FieldConfigManager()
        
        # 测试预设列表获取
        if hasattr(manager, 'get_field_presets'):
            presets = manager.get_field_presets()
            if isinstance(presets, dict) and len(presets) > 0:
                results['presets_list'] = "✅ 成功"
                print(f"✅ 字段预设获取成功，共 {len(presets)} 个预设")
                
                # 显示预设信息
                for preset_name, fields in presets.items():
                    print(f"   - {preset_name}: {len(fields)} 个字段")
            else:
                results['presets_list'] = "❌ 预设为空或格式错误"
                print("❌ 字段预设为空或格式错误")
        else:
            results['presets_list'] = "❌ 缺少预设获取方法"
            print("❌ 字段配置管理器缺少预设获取方法")
        
        # 测试预设字段获取
        if hasattr(manager, 'get_preset_fields'):
            basic_fields = manager.get_preset_fields('basic')
            if isinstance(basic_fields, list) and len(basic_fields) > 0:
                results['preset_fields'] = "✅ 成功"
                print(f"✅ 基础预设字段获取成功，包含 {len(basic_fields)} 个字段")
            else:
                results['preset_fields'] = "❌ 预设字段为空或格式错误"
                print("❌ 预设字段为空或格式错误")
        else:
            results['preset_fields'] = "❌ 缺少预设字段获取方法"
            print("❌ 字段配置管理器缺少预设字段获取方法")
        
    except Exception as e:
        results['field_presets_test'] = f"❌ 失败: {e}"
        print(f"❌ 字段预设测试失败: {e}")
        traceback.print_exc()
    
    return results

async def test_ai_analysis_with_fields():
    """测试带字段配置的AI分析"""
    print("\n🤖 测试带字段配置的AI分析...")
    print("="*60)
    
    results = {}
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        
        # 检查新的分析方法是否存在
        if hasattr(analyzer, 'analyze_with_field_config'):
            results['field_analysis_method'] = "✅ 成功"
            print("✅ 字段配置分析方法存在")
        else:
            results['field_analysis_method'] = "❌ 缺少方法"
            print("❌ 缺少字段配置分析方法")
            return results
        
        # 检查字段配置分析方法是否存在
        if hasattr(analyzer, 'analyze_article_page_selectors_with_config'):
            results['article_analysis_method'] = "✅ 成功"
            print("✅ 字段配置文章页分析方法存在")
        else:
            results['article_analysis_method'] = "❌ 缺少方法"
            print("❌ 缺少字段配置文章页分析方法")
        
        # 检查配置生成方法
        if hasattr(analyzer, '_generate_field_aware_config'):
            results['config_generation_method'] = "✅ 成功"
            print("✅ 字段感知配置生成方法存在")
        else:
            results['config_generation_method'] = "❌ 缺少方法"
            print("❌ 缺少字段感知配置生成方法")
        
    except Exception as e:
        results['ai_field_analysis_test'] = f"❌ 失败: {e}"
        print(f"❌ AI字段分析测试失败: {e}")
        traceback.print_exc()
    
    return results

def main():
    """主函数"""
    print("🚀 AI模块集成验证测试")
    print("="*70)
    
    all_results = {}
    
    # 运行所有测试
    all_results['ai_field_integration'] = test_ai_field_integration()
    all_results['field_presets'] = test_field_presets()
    
    try:
        all_results['ai_analysis_with_fields'] = asyncio.run(test_ai_analysis_with_fields())
    except Exception as e:
        print(f"❌ AI分析测试异常: {e}")
        all_results['ai_analysis_with_fields'] = {'error': str(e)}
    
    # 生成总结报告
    print("\n📊 集成测试总结")
    print("="*70)
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        print(f"\n{category}:")
        if isinstance(results, dict):
            for test_name, result in results.items():
                total_tests += 1
                if isinstance(result, str) and result.startswith("✅"):
                    passed_tests += 1
                    status = "通过"
                else:
                    status = "失败"
                print(f"  {test_name}: {status}")
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"\n总体结果: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 AI模块集成测试优秀！")
    elif success_rate >= 70:
        print("✅ AI模块集成测试良好")
    elif success_rate >= 50:
        print("⚠️ AI模块集成测试一般，需要改进")
    else:
        print("❌ AI模块集成测试失败，需要重大修复")

if __name__ == "__main__":
    main()
