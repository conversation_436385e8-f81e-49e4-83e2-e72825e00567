#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义字段功能
验证用户可以自己填写字段名和值的功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_custom_field_dialog():
    """测试自定义字段对话框"""
    print("=" * 60)
    print("测试自定义字段对话框")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CustomFieldDialog
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = CustomFieldDialog()
        print("✅ 自定义字段对话框创建成功")
        
        # 测试添加预设字段
        dialog.add_preset_field("author", "管理员")
        dialog.add_preset_field("category", "新闻")
        dialog.add_preset_field("status", "已发布")
        
        print("✅ 预设字段添加成功")
        
        # 测试获取自定义字段
        custom_fields = dialog.get_custom_fields()
        print(f"📊 获取到的自定义字段: {custom_fields}")
        
        expected_fields = {
            "author": "管理员",
            "category": "新闻", 
            "status": "已发布"
        }
        
        # 验证字段内容
        success = True
        for field_name, expected_value in expected_fields.items():
            if field_name in custom_fields:
                actual_value = custom_fields[field_name]
                if actual_value == expected_value:
                    print(f"   ✅ {field_name}: {actual_value}")
                else:
                    print(f"   ❌ {field_name}: 期望 '{expected_value}', 实际 '{actual_value}'")
                    success = False
            else:
                print(f"   ❌ {field_name}: 字段不存在")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("=" * 60)
    print("测试GUI集成")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 测试自定义字段保存
        test_custom_fields = {
            "author": "测试作者",
            "department": "测试部门",
            "priority": "高"
        }
        
        window.save_custom_fields(test_custom_fields)
        print("✅ 自定义字段保存成功")
        
        # 检查是否正确保存
        if hasattr(window, 'custom_fields'):
            saved_fields = window.custom_fields
            print(f"📊 保存的自定义字段: {saved_fields}")
            
            # 验证保存的字段
            success = True
            for field_name, expected_value in test_custom_fields.items():
                if field_name in saved_fields:
                    actual_value = saved_fields[field_name]
                    if actual_value == expected_value:
                        print(f"   ✅ {field_name}: {actual_value}")
                    else:
                        print(f"   ❌ {field_name}: 期望 '{expected_value}', 实际 '{actual_value}'")
                        success = False
                else:
                    print(f"   ❌ {field_name}: 字段不存在")
                    success = False
            
            return success
        else:
            print("❌ 自定义字段属性不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_integration():
    """测试配置集成"""
    print("=" * 60)
    print("测试配置集成")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        
        # 设置自定义字段
        test_custom_fields = {
            "source_type": "官方",
            "importance": "重要",
            "reviewer": "审核员"
        }
        
        window.save_custom_fields(test_custom_fields)
        
        # 获取字段配置
        field_config = window.get_field_config_from_gui()
        print(f"📊 字段配置: {field_config}")
        
        # 检查自定义字段是否包含在配置中
        if 'custom_fields' in field_config:
            custom_fields_in_config = field_config['custom_fields']
            print(f"📝 配置中的自定义字段: {custom_fields_in_config}")
            
            # 验证字段
            success = True
            for field_name, expected_value in test_custom_fields.items():
                if field_name in custom_fields_in_config:
                    actual_value = custom_fields_in_config[field_name]
                    if actual_value == expected_value:
                        print(f"   ✅ {field_name}: {actual_value}")
                    else:
                        print(f"   ❌ {field_name}: 期望 '{expected_value}', 实际 '{actual_value}'")
                        success = False
                else:
                    print(f"   ❌ {field_name}: 字段不存在")
                    success = False
            
            return success
        else:
            print("❌ 配置中没有自定义字段")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_config():
    """测试爬虫配置"""
    print("=" * 60)
    print("测试爬虫配置")
    print("=" * 60)
    
    try:
        from gui.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 模拟GUI配置
        gui_config = {
            'input_url': 'http://example.com',
            'base_url': 'http://example.com',
            'custom_fields': {
                'author': '系统管理员',
                'category': '系统公告',
                'level': '重要'
            },
            'use_field_config': True
        }
        
        # 准备爬虫配置
        crawler_config = config_manager.prepare_crawler_config(gui_config)
        print(f"📊 爬虫配置: {crawler_config}")
        
        # 检查用户自定义字段是否正确传递
        if 'user_custom_fields' in crawler_config:
            user_custom_fields = crawler_config['user_custom_fields']
            print(f"📝 爬虫配置中的用户自定义字段: {user_custom_fields}")
            
            expected_fields = gui_config['custom_fields']
            
            # 验证字段
            success = True
            for field_name, expected_value in expected_fields.items():
                if field_name in user_custom_fields:
                    actual_value = user_custom_fields[field_name]
                    if actual_value == expected_value:
                        print(f"   ✅ {field_name}: {actual_value}")
                    else:
                        print(f"   ❌ {field_name}: 期望 '{expected_value}', 实际 '{actual_value}'")
                        success = False
                else:
                    print(f"   ❌ {field_name}: 字段不存在")
                    success = False
            
            return success
        else:
            print("❌ 爬虫配置中没有用户自定义字段")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试自定义字段功能")
    print("📝 用户可以自己填写字段名和值，类似classid的使用方式")
    print()
    
    tests = [
        ("自定义字段对话框测试", test_custom_field_dialog),
        ("GUI集成测试", test_gui_integration),
        ("配置集成测试", test_config_integration),
        ("爬虫配置测试", test_crawler_config),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！自定义字段功能正常。")
        print("\n💡 功能说明:")
        print("   ✅ 用户可以通过对话框添加自定义字段")
        print("   ✅ 支持预设字段快速添加")
        print("   ✅ 自定义字段会保存到GUI配置中")
        print("   ✅ 爬虫配置正确传递用户自定义字段")
        print("   ✅ 自定义字段会添加到每条爬取的数据中")
        print("\n🎯 使用方式:")
        print("   1. 在字段配置页面点击'自定义字段'按钮")
        print("   2. 在对话框中添加字段名和值")
        print("   3. 可以使用预设字段快速添加常用字段")
        print("   4. 确定后字段会自动添加到爬取数据中")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
