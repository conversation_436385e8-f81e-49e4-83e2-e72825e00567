#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复结果的脚本
验证模组配置JSON文件读取和txt_clear.py的非文本字段清洗功能
"""

import sys
import os
import json

def test_module_config_loading():
    """测试模组配置加载"""
    print("=" * 60)
    print("🧪 测试模组配置JSON文件读取")
    print("=" * 60)
    
    try:
        # 测试模组管理器导入
        from modules.manager import ModuleManager, module_manager
        print("✅ 模组管理器导入成功")
        
        # 测试配置文件加载
        config_file = "configs/modules/module_configs.json"
        if os.path.exists(config_file):
            print(f"✅ 配置文件存在: {config_file}")
            
            # 直接测试JSON读取
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ JSON文件读取成功，包含 {len(data)} 个配置项")
            
            # 测试模组管理器实例化
            manager = ModuleManager()
            print(f"✅ 模组管理器实例化成功")
            print(f"   - 加载的模组数量: {len(manager.modules)}")
            print(f"   - 模组列表: {list(manager.modules.keys())}")
            
            # 测试全局实例
            print(f"✅ 全局模组管理器可用")
            print(f"   - 全局实例模组数量: {len(module_manager.modules)}")
            
        else:
            print(f"❌ 配置文件不存在: {config_file}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True


def test_basic_config_loading():
    """测试基本配置组加载"""
    print("\n" + "=" * 60)
    print("🧪 测试基本配置组JSON文件读取")
    print("=" * 60)
    
    try:
        from config.manager import ConfigManager
        print("✅ 配置管理器导入成功")
        
        # 测试配置加载
        config_manager = ConfigManager()
        print("✅ 配置管理器实例化成功")
        
        groups = config_manager.get_groups()
        print(f"✅ 配置组加载成功，包含 {len(groups)} 个配置组")
        print(f"   - 配置组列表: {groups}")
        
        current_group = config_manager.get_current_group()
        print(f"✅ 当前配置组: {current_group}")
        
        if current_group:
            group_config = config_manager.get_group(current_group)
            if group_config:
                print(f"✅ 配置组详情加载成功")
                print(f"   - 配置项数量: {len(group_config)}")
            else:
                print(f"⚠️ 配置组详情为空")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True


def test_txt_clear_functions():
    """测试txt_clear.py的新增功能"""
    print("\n" + "=" * 60)
    print("🧪 测试txt_clear.py非文本字段清洗功能")
    print("=" * 60)
    
    try:
        from core.txt_clear import (
            clean_non_text_fields,
            clean_string_field,
            clean_html_tags,
            clean_special_characters,
            clean_numeric_field
        )
        print("✅ txt_clear新功能导入成功")
        
        # 测试HTML标签清洗
        html_text = "<p>这是一个<strong>测试</strong>文本&nbsp;&amp;符号</p>"
        cleaned_html = clean_html_tags(html_text)
        print(f"✅ HTML标签清洗测试:")
        print(f"   原文: {html_text}")
        print(f"   清洗后: {cleaned_html}")
        
        # 测试特殊字符清洗
        special_text = "测试文本\u200b\ufeff包含特殊字符★☆"
        cleaned_special = clean_special_characters(special_text)
        print(f"✅ 特殊字符清洗测试:")
        print(f"   原文: {repr(special_text)}")
        print(f"   清洗后: {repr(cleaned_special)}")
        
        # 测试数字字段清洗
        number_field = 123.4500
        cleaned_number = clean_numeric_field(number_field)
        print(f"✅ 数字字段清洗测试:")
        print(f"   原数字: {number_field}")
        print(f"   清洗后: {cleaned_number}")
        
        # URL清洗功能已删除，避免过度清洗导致URL截断
        print(f"✅ URL清洗功能已删除，避免过度清洗")
        
        # 测试综合清洗
        complex_data = {
            "title": "<h1>测试标题&nbsp;</h1>",
            "content": "内容包含\u200b特殊字符★和<p>HTML标签</p>",
            "number": 123.000
        }
        cleaned_data = clean_non_text_fields(complex_data)
        print(f"✅ 综合清洗测试:")
        print(f"   原数据: {complex_data}")
        print(f"   清洗后: {cleaned_data}")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始测试修复结果")
    
    results = []
    
    # 测试模组配置
    results.append(test_module_config_loading())
    
    # 测试基本配置
    results.append(test_basic_config_loading())
    
    # 测试txt_clear功能
    results.append(test_txt_clear_functions())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
