#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字段配置覆盖功能
验证字段配置开关是否能正确覆盖原有的选择器
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_field_selector_override():
    """测试字段选择器覆盖功能"""
    print("=" * 60)
    print("测试字段选择器覆盖功能")
    print("=" * 60)
    
    try:
        # 导入相关模块
        from core.crawler import _apply_field_selectors, _get_field_selectors, _field_selector_overrides
        from core.field_config_manager import get_field_config_manager
        
        print("✅ 模块导入成功")
        
        # 获取字段配置管理器
        manager = get_field_config_manager()
        all_fields = manager.get_available_fields()
        print(f"📊 可用字段数量: {len(all_fields)}")
        
        # 测试字段配置
        test_field_configs = {
            'title': {
                'selectors': ['.custom-title', '.my-title', 'h1.title']
            },
            'content': {
                'selectors': ['.custom-content', '.my-content', '.article-body']
            },
            'likes': {
                'selectors': ['.custom-likes', '.my-likes', '[data-likes]']
            }
        }
        
        print(f"\n🧪 测试字段配置: {list(test_field_configs.keys())}")
        
        # 应用字段配置
        _apply_field_selectors(test_field_configs)
        
        print(f"📝 覆盖的字段数量: {len(_field_selector_overrides)}")
        print(f"📋 覆盖的字段: {list(_field_selector_overrides.keys())}")
        
        # 测试选择器获取
        print("\n🔍 测试选择器获取:")
        
        # 测试被覆盖的字段
        title_selectors = _get_field_selectors('title', ['default-title'])
        print(f"   title选择器: {title_selectors}")
        expected_title = ['.custom-title', '.my-title', 'h1.title']
        title_correct = title_selectors == expected_title
        print(f"   title覆盖: {'✅ 正确' if title_correct else '❌ 错误'}")
        
        content_selectors = _get_field_selectors('content', ['default-content'])
        print(f"   content选择器: {content_selectors}")
        expected_content = ['.custom-content', '.my-content', '.article-body']
        content_correct = content_selectors == expected_content
        print(f"   content覆盖: {'✅ 正确' if content_correct else '❌ 错误'}")
        
        likes_selectors = _get_field_selectors('likes', ['default-likes'])
        print(f"   likes选择器: {likes_selectors}")
        expected_likes = ['.custom-likes', '.my-likes', '[data-likes]']
        likes_correct = likes_selectors == expected_likes
        print(f"   likes覆盖: {'✅ 正确' if likes_correct else '❌ 错误'}")
        
        # 测试未被覆盖的字段
        views_selectors = _get_field_selectors('views', ['default-views'])
        print(f"   views选择器: {views_selectors}")
        views_correct = views_selectors == ['default-views']
        print(f"   views默认: {'✅ 正确' if views_correct else '❌ 错误'}")
        
        # 清空覆盖配置
        _field_selector_overrides.clear()
        print(f"\n🔄 清空覆盖配置后:")
        
        # 再次测试
        title_selectors_after = _get_field_selectors('title', ['default-title'])
        print(f"   title选择器: {title_selectors_after}")
        title_default_correct = title_selectors_after == ['default-title']
        print(f"   title恢复默认: {'✅ 正确' if title_default_correct else '❌ 错误'}")
        
        # 总结测试结果
        all_correct = all([
            title_correct, content_correct, likes_correct, 
            views_correct, title_default_correct
        ])
        
        print(f"\n📊 测试结果: {'✅ 全部通过' if all_correct else '❌ 部分失败'}")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_preset_override():
    """测试字段预设覆盖功能"""
    print("=" * 60)
    print("测试字段预设覆盖功能")
    print("=" * 60)
    
    try:
        from core.crawler import _apply_field_selectors, _get_field_selectors, _field_selector_overrides
        from core.field_config_manager import get_field_config_manager
        
        # 清空之前的覆盖配置
        _field_selector_overrides.clear()
        
        # 获取字段配置管理器
        manager = get_field_config_manager()
        
        # 测试社交媒体预设
        presets = manager.get_field_presets()
        print(f"📊 可用预设: {list(presets.keys())}")
        
        if 'social_media' in presets:
            print(f"\n🧪 测试社交媒体预设")
            
            # 获取预设字段
            preset_fields = presets['social_media']
            print(f"📋 预设字段: {preset_fields}")
            
            # 获取字段配置
            all_fields = manager.get_available_fields()
            field_configs = {}
            for field_name in preset_fields:
                if field_name in all_fields:
                    field_configs[field_name] = all_fields[field_name]
            
            print(f"📝 字段配置数量: {len(field_configs)}")
            
            # 应用字段配置
            _apply_field_selectors(field_configs)
            
            print(f"🔧 覆盖的字段: {list(_field_selector_overrides.keys())}")
            
            # 测试几个关键字段
            test_fields = ['title', 'content', 'likes', 'views']
            for field_name in test_fields:
                if field_name in _field_selector_overrides:
                    selectors = _get_field_selectors(field_name, ['default'])
                    print(f"   {field_name}: {len(selectors)} 个选择器")
                    print(f"      {selectors[:2]}...")  # 显示前两个
                else:
                    print(f"   {field_name}: 未覆盖")
            
            return True
        else:
            print("❌ 社交媒体预设不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("=" * 60)
    print("测试GUI集成")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 启用字段配置
        if hasattr(window, 'enable_field_config_checkbox'):
            window.enable_field_config_checkbox.setChecked(True)
            print("✅ 字段配置开关已启用")
            
            # 获取字段配置
            field_config = window.get_field_config_from_gui()
            print(f"📊 字段配置: {field_config}")
            
            use_field_config = field_config.get('use_field_config', False)
            print(f"🔧 使用字段配置: {'✅ 是' if use_field_config else '❌ 否'}")
            
            if use_field_config:
                field_preset = field_config.get('field_preset', '')
                custom_field_list = field_config.get('custom_field_list', [])
                
                print(f"📋 字段预设: {field_preset}")
                print(f"📝 自定义字段: {len(custom_field_list)} 个")
                
                return True
            else:
                print("❌ 字段配置未启用")
                return False
        else:
            print("❌ 字段配置开关不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试字段配置覆盖功能")
    print()
    
    tests = [
        ("字段选择器覆盖测试", test_field_selector_override),
        ("字段预设覆盖测试", test_field_preset_override),
        ("GUI集成测试", test_gui_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！字段配置覆盖功能正常。")
        print("\n💡 功能说明:")
        print("   ✅ 字段配置开关启用时会覆盖原有选择器")
        print("   ✅ 支持字段预设和自定义字段列表")
        print("   ✅ 未配置的字段使用默认选择器")
        print("   ✅ GUI集成正常工作")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
