#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块Playwright迁移测试
验证AI模块是否成功从Selenium迁移到Playwright
"""

import asyncio
import time
import traceback

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试AI模块导入
        import AI_wed_find_agent
        print("✅ AI_wed_find_agent 导入成功")
        
        # 测试Playwright导入
        from playwright.async_api import async_playwright
        print("✅ Playwright 导入成功")
        
        # 测试crawler模块导入
        import crawler
        print("✅ crawler 模块导入成功")
        
        # 检查是否还有Selenium残留
        try:
            import selenium_diver_change
            print("❌ selenium_diver_change 仍然存在，应该已被删除")
            return False
        except ImportError:
            print("✅ selenium_diver_change 已成功删除")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_function_signatures():
    """测试函数签名"""
    print("\n🔍 测试函数签名...")
    
    try:
        import AI_wed_find_agent
        import inspect
        
        # 检查analyze_page_with_selenium函数
        if hasattr(AI_wed_find_agent, 'analyze_page_with_selenium'):
            print("✅ analyze_page_with_selenium 函数存在")
        else:
            print("❌ analyze_page_with_selenium 函数缺失")
            return False
        
        # 检查extract_first_article_url函数
        if hasattr(AI_wed_find_agent, 'extract_first_article_url'):
            print("✅ extract_first_article_url 函数存在")
        else:
            print("❌ extract_first_article_url 函数缺失")
            return False
        
        # 检查新的Playwright函数
        if hasattr(AI_wed_find_agent, 'analyze_page_with_playwright'):
            print("✅ analyze_page_with_playwright 函数存在")
        else:
            print("❌ analyze_page_with_playwright 函数缺失")
            return False
        
        if hasattr(AI_wed_find_agent, 'extract_first_article_url_playwright'):
            print("✅ extract_first_article_url_playwright 函数存在")
        else:
            print("❌ extract_first_article_url_playwright 函数缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 函数签名测试失败: {e}")
        return False

async def test_playwright_functionality():
    """测试Playwright功能"""
    print("\n🔍 测试Playwright功能...")
    
    try:
        import AI_wed_find_agent
        
        # 测试页面分析功能
        test_url = "https://httpbin.org/html"  # 简单的测试页面
        
        print(f"📄 测试页面分析: {test_url}")
        start_time = time.time()
        
        # 测试新的Playwright函数
        page_data = await AI_wed_find_agent.analyze_page_with_playwright(test_url)
        
        end_time = time.time()
        
        if page_data and 'title' in page_data and 'html' in page_data:
            print(f"✅ 页面分析成功")
            print(f"   标题: {page_data['title'][:50]}...")
            print(f"   HTML长度: {len(page_data['html'])} 字符")
            print(f"   耗时: {end_time - start_time:.2f} 秒")
            return True
        else:
            print("❌ 页面分析失败：返回数据不完整")
            return False
            
    except Exception as e:
        print(f"❌ Playwright功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_compatibility_wrapper():
    """测试兼容性包装函数"""
    print("\n🔍 测试兼容性包装函数...")
    
    try:
        import AI_wed_find_agent
        
        # 测试兼容性包装函数
        test_url = "https://httpbin.org/html"
        
        print(f"📄 测试兼容性函数: {test_url}")
        start_time = time.time()
        
        # 使用原有函数名调用
        page_data = AI_wed_find_agent.analyze_page_with_selenium(test_url)
        
        end_time = time.time()
        
        if page_data and 'title' in page_data and 'html' in page_data:
            print(f"✅ 兼容性函数调用成功")
            print(f"   标题: {page_data['title'][:50]}...")
            print(f"   HTML长度: {len(page_data['html'])} 字符")
            print(f"   耗时: {end_time - start_time:.2f} 秒")
            return True
        else:
            print("❌ 兼容性函数调用失败：返回数据不完整")
            return False
            
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🔍 测试GUI集成...")
    
    try:
        # 测试gui_ai_helper模块
        import gui_ai_helper
        print("✅ gui_ai_helper 导入成功")
        
        # 检查AIAutoConfigHelper类
        if hasattr(gui_ai_helper, 'AIAutoConfigHelper'):
            print("✅ AIAutoConfigHelper 类存在")
        else:
            print("❌ AIAutoConfigHelper 类缺失")
            return False
        
        # 检查AIConfigManager类
        if hasattr(gui_ai_helper, 'AIConfigManager'):
            print("✅ AIConfigManager 类存在")
        else:
            print("❌ AIConfigManager 类缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def test_crawler_integration():
    """测试crawler集成"""
    print("\n🔍 测试crawler集成...")
    
    try:
        import crawler
        
        # 检查关键的Playwright函数
        required_functions = [
            'launch_browser',
            'get_article_links_playwright'
        ]
        
        for func_name in required_functions:
            if hasattr(crawler, func_name):
                print(f"✅ crawler.{func_name} 函数存在")
            else:
                print(f"❌ crawler.{func_name} 函数缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ crawler集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("AI模块Playwright迁移测试")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("函数签名测试", test_function_signatures),
        ("Playwright功能测试", test_playwright_functionality),
        ("兼容性包装测试", test_compatibility_wrapper),
        ("GUI集成测试", test_gui_integration),
        ("crawler集成测试", test_crawler_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！AI模块Playwright迁移成功。")
        print("\n迁移成果:")
        print("✅ 完全移除Selenium依赖")
        print("✅ 统一使用Playwright技术栈")
        print("✅ 保持向后兼容性")
        print("✅ 提升性能和稳定性")
        print("✅ 简化维护复杂度")
        
        print("\n现在可以:")
        print("1. 使用AI智能配置功能")
        print("2. 享受更快的页面分析速度")
        print("3. 获得更稳定的爬取体验")
        print("4. 统一的技术栈管理")
        
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")
        print("可能的问题:")
        print("- Playwright安装不完整")
        print("- 函数签名不匹配")
        print("- 异步调用问题")

if __name__ == "__main__":
    asyncio.run(main())
