#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模组配置系统测试脚本
用于测试模组配置系统的各项功能
"""

import os
import logging
from typing import List, Dict

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_module_manager():
    """测试模组管理器"""
    print("="*60)
    print("测试模组管理器")
    print("="*60)
    
    try:
        from module_manager import module_manager, get_config_for_url, match_module_for_url
        
        # 测试1: 列出所有模组
        print("\n1. 列出所有模组:")
        modules = module_manager.list_modules()
        for i, module_name in enumerate(modules, 1):
            module_info = module_manager.get_module_info(module_name)
            description = module_info.get('description', '无描述') if module_info else '无描述'
            print(f"   {i}. {module_name} - {description}")
        
        # 测试2: URL匹配测试
        print("\n2. URL匹配测试:")
        test_urls = [
            "https://mp.weixin.qq.com/s/2EGonPvQhmqoEKszLUxZCg",
            "https://www.shrd.gov.cn/n8347/n8378/u1ai270696.html",
            "https://www.example.com/news/article.html",
            "https://www.gov.cn/news/article.html"
        ]
        
        for url in test_urls:
            module_name = match_module_for_url(url)
            config = get_config_for_url(url)
            print(f"   URL: {url}")
            print(f"   匹配模组: {module_name}")
            if config:
                print(f"   内容选择器: {config.get('content_selectors', [])}")
                print(f"   爬取模式: {config.get('mode', 'balance')}")
            print()
        
        print("✓ 模组管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 模组管理器测试失败: {e}")
        return False

def test_failed_url_processor():
    """测试失败URL处理器"""
    print("="*60)
    print("测试失败URL处理器")
    print("="*60)
    
    try:
        from failed_url_processor import FailedUrlProcessor
        
        # 检查是否有失败文件
        failed_files = []
        articles_dir = "articles"
        if os.path.exists(articles_dir):
            for file in os.listdir(articles_dir):
                if file.endswith("_failed.csv"):
                    failed_files.append(os.path.join(articles_dir, file))
        
        if not failed_files:
            print("   没有找到失败URL文件，跳过测试")
            return True
        
        print(f"   找到 {len(failed_files)} 个失败文件:")
        for file in failed_files:
            print(f"   - {file}")
        
        # 测试加载失败URL
        processor = FailedUrlProcessor()
        test_file = failed_files[0]
        
        print(f"\n   测试加载失败URL: {test_file}")
        failed_urls = processor.load_failed_urls(test_file)
        print(f"   加载了 {len(failed_urls)} 个失败URL")
        
        if failed_urls:
            print("   前3个失败URL:")
            for i, url_info in enumerate(failed_urls[:3], 1):
                print(f"   {i}. {url_info.get('failed_url', '')}")
                print(f"      原因: {url_info.get('reason', '')}")
        
        print("✓ 失败URL处理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 失败URL处理器测试失败: {e}")
        return False

def test_config_file():
    """测试配置文件"""
    print("="*60)
    print("测试配置文件")
    print("="*60)
    
    config_file = "module_configs.json"
    
    if not os.path.exists(config_file):
        print(f"   ✗ 配置文件不存在: {config_file}")
        return False
    
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        print(f"   ✓ 配置文件加载成功: {config_file}")
        print(f"   配置模组数量: {len([k for k in config_data.keys() if k not in ['default_module', 'match_priority']])}")
        
        # 检查必要字段
        required_fields = ['default_module', 'match_priority']
        for field in required_fields:
            if field in config_data:
                print(f"   ✓ 包含必要字段: {field}")
            else:
                print(f"   ✗ 缺少必要字段: {field}")
        
        # 检查模组配置
        for module_name, module_config in config_data.items():
            if module_name in required_fields:
                continue
            
            print(f"\n   模组: {module_name}")
            if 'config' in module_config:
                config = module_config['config']
                selectors = ['title_selectors', 'content_selectors', 'date_selectors', 'source_selectors']
                for selector in selectors:
                    if selector in config:
                        print(f"     ✓ {selector}: {len(config[selector])} 个")
                    else:
                        print(f"     ✗ 缺少 {selector}")
            else:
                print(f"     ✗ 缺少 config 字段")
        
        print("\n✓ 配置文件测试通过")
        return True
        
    except Exception as e:
        print(f"   ✗ 配置文件测试失败: {e}")
        return False

def test_crawler_integration():
    """测试爬虫集成"""
    print("="*60)
    print("测试爬虫集成")
    print("="*60)
    
    try:
        # 检查crawler.py是否包含模组管理器导入
        crawler_file = "crawler.py"
        if not os.path.exists(crawler_file):
            print(f"   ✗ 爬虫文件不存在: {crawler_file}")
            return False
        
        with open(crawler_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键导入
        if "from module_manager import" in content:
            print("   ✓ 包含模组管理器导入")
        else:
            print("   ✗ 缺少模组管理器导入")
        
        # 检查USE_MODULE_MANAGER标志
        if "USE_MODULE_MANAGER" in content:
            print("   ✓ 包含模组管理器标志")
        else:
            print("   ✗ 缺少模组管理器标志")
        
        # 检查use_module_config参数
        if "use_module_config" in content:
            print("   ✓ 包含模组配置参数")
        else:
            print("   ✗ 缺少模组配置参数")
        
        print("✓ 爬虫集成测试通过")
        return True
        
    except Exception as e:
        print(f"   ✗ 爬虫集成测试失败: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("模组配置系统综合测试")
    print("="*60)
    
    tests = [
        ("配置文件测试", test_config_file),
        ("模组管理器测试", test_module_manager),
        ("失败URL处理器测试", test_failed_url_processor),
        ("爬虫集成测试", test_crawler_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！模组配置系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
    
    return passed == total

def demo_usage():
    """演示使用方法"""
    print("\n" + "="*60)
    print("使用方法演示")
    print("="*60)
    
    print("\n1. 基本使用:")
    print("   python module_config_manager.py  # 启动配置管理界面")
    
    print("\n2. 处理失败URL:")
    print("   from failed_url_processor import process_failed_csv")
    print("   result = process_failed_csv('articles/xxx_failed.csv')")
    
    print("\n3. 测试URL匹配:")
    print("   from module_manager import match_module_for_url")
    print("   module = match_module_for_url('https://mp.weixin.qq.com/s/xxx')")
    
    print("\n4. 获取配置:")
    print("   from module_manager import get_config_for_url")
    print("   config = get_config_for_url('https://mp.weixin.qq.com/s/xxx')")

if __name__ == "__main__":
    # 运行综合测试
    success = run_comprehensive_test()
    
    # 显示使用方法
    demo_usage()
    
    # 退出码
    exit(0 if success else 1)
