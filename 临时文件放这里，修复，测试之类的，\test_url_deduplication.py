#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试URL去重功能的脚本
验证crawl_articles_async函数中的URL去重处理
"""

import sys
import os

def test_url_deduplication():
    """测试URL去重功能"""
    print("=" * 60)
    print("🧪 测试URL去重功能")
    print("=" * 60)
    
    try:
        from core.crawler import deduplicate_articles_by_url, normalize_url_for_deduplication
        print("✅ URL去重函数导入成功")
        
        # 测试数据：包含重复URL的文章列表
        test_articles = [
            {"title": "文章1", "url": "https://example.com/article1"},
            {"title": "文章2", "url": "https://example.com/article2"},
            {"title": "文章3", "url": "https://example.com/article1"},  # 重复URL
            {"title": "文章4", "url": "https://example.com/article3?utm_source=test"},
            {"title": "文章5", "url": "https://example.com/article3?from=mobile"},  # 规范化后重复
            {"title": "文章6", "url": "https://example.com/article4#section1"},
            {"title": "文章7", "url": "https://example.com/article4#section2"},  # 规范化后重复
            {"title": "文章8", "url": "https://example.com/article5"},
            {"title": "文章9", "href": "https://example.com/article6"},  # 使用href字段
            {"title": "文章10", "link": "https://example.com/article7"},  # 使用link字段
            {"title": "文章11"},  # 无URL字段
        ]
        
        print(f"📊 测试数据: {len(test_articles)} 篇文章")
        for i, article in enumerate(test_articles, 1):
            url = article.get('url') or article.get('href') or article.get('link') or '无URL'
            print(f"  {i}. {article['title']}: {url}")
        
        # 执行去重
        print("\n🔄 执行URL去重...")
        deduplicated = deduplicate_articles_by_url(test_articles)
        
        print(f"\n📊 去重结果: {len(deduplicated)} 篇文章")
        for i, article in enumerate(deduplicated, 1):
            url = article.get('url') or article.get('href') or article.get('link') or '无URL'
            print(f"  {i}. {article['title']}: {url}")
        
        # 验证结果
        expected_count = 8  # 预期去重后应该有8篇文章
        if len(deduplicated) == expected_count:
            print(f"✅ 去重测试通过: 预期 {expected_count} 篇，实际 {len(deduplicated)} 篇")
        else:
            print(f"❌ 去重测试失败: 预期 {expected_count} 篇，实际 {len(deduplicated)} 篇")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_url_normalization():
    """测试URL规范化功能"""
    print("\n" + "=" * 60)
    print("🧪 测试URL规范化功能")
    print("=" * 60)
    
    try:
        from core.crawler import normalize_url_for_deduplication
        print("✅ URL规范化函数导入成功")
        
        # 测试用例
        test_cases = [
            {
                "input": "https://example.com/article?utm_source=test&param=value",
                "expected": "https://example.com/article?param=value",
                "description": "移除UTM参数"
            },
            {
                "input": "https://example.com/article#section1",
                "expected": "https://example.com/article",
                "description": "移除片段标识符"
            },
            {
                "input": "https://example.com/article?from=mobile&sessionid=123",
                "expected": "https://example.com/article",
                "description": "移除跟踪参数"
            },
            {
                "input": "https://example.com/article?param=value&utm_campaign=test#section",
                "expected": "https://example.com/article?param=value",
                "description": "移除UTM参数和片段"
            },
            {
                "input": "https://example.com/article",
                "expected": "https://example.com/article",
                "description": "无需处理的URL"
            }
        ]
        
        all_passed = True
        for i, case in enumerate(test_cases, 1):
            result = normalize_url_for_deduplication(case["input"])
            if result == case["expected"]:
                print(f"✅ 测试 {i}: {case['description']}")
                print(f"   输入: {case['input']}")
                print(f"   输出: {result}")
            else:
                print(f"❌ 测试 {i}: {case['description']}")
                print(f"   输入: {case['input']}")
                print(f"   预期: {case['expected']}")
                print(f"   实际: {result}")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("🧪 测试边界情况")
    print("=" * 60)
    
    try:
        from core.crawler import deduplicate_articles_by_url
        print("✅ URL去重函数导入成功")
        
        # 测试空列表
        result = deduplicate_articles_by_url([])
        if len(result) == 0:
            print("✅ 空列表测试通过")
        else:
            print("❌ 空列表测试失败")
            return False
        
        # 测试None
        result = deduplicate_articles_by_url(None)
        if result is None:
            print("✅ None输入测试通过")
        else:
            print("❌ None输入测试失败")
            return False
        
        # 测试无URL字段的文章
        articles_no_url = [
            {"title": "文章1"},
            {"title": "文章2", "content": "内容"},
            {"title": "文章3", "url": ""},  # 空URL
            {"title": "文章4", "url": None},  # None URL
        ]
        
        result = deduplicate_articles_by_url(articles_no_url)
        if len(result) == 4:  # 应该保留所有文章
            print("✅ 无URL字段测试通过")
        else:
            print(f"❌ 无URL字段测试失败: 预期4篇，实际{len(result)}篇")
            return False
        
        # 测试不同数据结构
        class ArticleObj:
            def __init__(self, title, url):
                self.title = title
                self.url = url
        
        mixed_articles = [
            {"title": "字典文章", "url": "https://example.com/dict"},
            ArticleObj("对象文章", "https://example.com/obj"),
            {"title": "重复字典", "url": "https://example.com/dict"},  # 重复
        ]
        
        result = deduplicate_articles_by_url(mixed_articles)
        if len(result) == 2:  # 应该去重后剩2篇
            print("✅ 混合数据结构测试通过")
        else:
            print(f"❌ 混合数据结构测试失败: 预期2篇，实际{len(result)}篇")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试URL去重功能")
    
    results = []
    
    # 基本去重测试
    results.append(test_url_deduplication())
    
    # URL规范化测试
    results.append(test_url_normalization())
    
    # 边界情况测试
    results.append(test_edge_cases())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！URL去重功能正常！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
