#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件结构
"""

def check_excel_file(file_path):
    """检查Excel文件结构"""
    try:
        import openpyxl
        print(f"📊 检查文件: {file_path}")
        
        wb = openpyxl.load_workbook(file_path)
        ws = wb.active
        
        print(f"📋 文件信息:")
        print(f"  - 行数: {ws.max_row}")
        print(f"  - 列数: {ws.max_column}")
        
        print(f"\n📝 表头信息:")
        headers = []
        for col in range(1, min(ws.max_column + 1, 10)):
            cell_value = ws.cell(row=1, column=col).value
            headers.append(str(cell_value) if cell_value else f"空列{col}")
            print(f"  第{col}列: {cell_value}")
        
        # 查找URL列
        url_column = None
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=1, column=col).value
            if cell_value and ('url' in str(cell_value).lower() or 'link' in str(cell_value).lower()):
                url_column = col
                print(f"\n🔍 找到URL列: 第{col}列 ({cell_value})")
                break
        
        if url_column is None:
            print(f"\n⚠️ 未找到URL列，将使用第1列")
            url_column = 1
        
        # 显示前几行数据
        print(f"\n📄 前5行数据预览:")
        for row in range(1, min(ws.max_row + 1, 6)):
            row_data = []
            for col in range(1, min(ws.max_column + 1, 5)):
                cell_value = ws.cell(row=row, column=col).value
                row_data.append(str(cell_value) if cell_value else "空")
            print(f"  第{row}行: {row_data}")
        
        # 统计URL数量
        url_count = 0
        for row in range(2, ws.max_row + 1):
            url = ws.cell(row=row, column=url_column).value
            if url and str(url).strip():
                url_count += 1
        
        print(f"\n📊 统计信息:")
        print(f"  - URL列: 第{url_column}列")
        print(f"  - 有效URL数量: {url_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 检查失败文件
    file_path = "articles/上海人大_代表风采_failed.xlsx"
    check_excel_file(file_path)
