#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI字段配置集成
验证字段配置功能在GUI中是否正常工作
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_field_config_import():
    """测试字段配置模块导入"""
    print("=" * 60)
    print("测试字段配置模块导入")
    print("=" * 60)
    
    try:
        from core.field_config_manager import (get_field_config_manager, apply_field_preset, 
                                              apply_custom_field_list, get_field_presets,
                                              get_available_field_names)
        print("✅ 字段配置管理器导入成功")
        
        # 测试获取预设
        presets = get_field_presets()
        print(f"📋 可用预设: {list(presets.keys())}")
        
        # 测试获取字段名称
        field_names = get_available_field_names()
        print(f"📊 可用字段数量: {len(field_names)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 字段配置模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 字段配置测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("=" * 60)
    print("测试GUI集成")
    print("=" * 60)
    
    try:
        # 测试PyQt5导入
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        print("✅ PyQt5 导入成功")
        
        # 测试GUI主窗口导入
        from gui.main_window import CrawlerGUI
        print("✅ GUI主窗口导入成功")
        
        # 创建应用程序实例（不显示窗口）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口实例
        window = CrawlerGUI()
        print("✅ GUI主窗口创建成功")
        
        # 检查字段配置相关属性
        has_fields_tab = hasattr(window, 'fields_tab')
        has_field_preset_combo = hasattr(window, 'field_preset_combo')
        has_field_checkboxes = hasattr(window, 'field_checkboxes')
        
        print(f"📋 字段配置标签页: {'✅' if has_fields_tab else '❌'}")
        print(f"📋 字段预设选择器: {'✅' if has_field_preset_combo else '❌'}")
        print(f"📋 字段复选框: {'✅' if has_field_checkboxes else '❌'}")
        
        # 检查字段配置方法
        has_apply_preset = hasattr(window, 'apply_field_preset')
        has_apply_custom = hasattr(window, 'apply_custom_fields')
        has_get_field_config = hasattr(window, 'get_field_config_from_gui')
        
        print(f"🔧 应用预设方法: {'✅' if has_apply_preset else '❌'}")
        print(f"🔧 应用自定义字段方法: {'✅' if has_apply_custom else '❌'}")
        print(f"🔧 获取字段配置方法: {'✅' if has_get_field_config else '❌'}")
        
        # 测试字段配置获取
        if has_get_field_config:
            try:
                field_config = window.get_field_config_from_gui()
                print(f"📊 字段配置获取成功: {field_config}")
            except Exception as e:
                print(f"⚠️ 字段配置获取失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ GUI导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_thread_integration():
    """测试爬虫线程集成"""
    print("=" * 60)
    print("测试爬虫线程集成")
    print("=" * 60)
    
    try:
        from gui.crawler_thread import CrawlerThread
        print("✅ 爬虫线程导入成功")
        
        # 创建测试配置
        test_config = {
            'input_url': 'https://example.com',
            'content_selectors': ['.content'],
            'field_config': {
                'use_field_config': True,
                'field_preset': 'basic',
                'custom_field_list': []
            }
        }
        
        # 创建爬虫线程实例
        crawler_thread = CrawlerThread(test_config)
        print("✅ 爬虫线程创建成功")
        
        # 测试配置准备
        prepared_config = crawler_thread._prepare_crawler_config()
        print(f"📊 配置准备成功，参数数量: {len(prepared_config)}")
        
        # 检查字段配置相关参数
        field_params = ['field_preset', 'custom_field_list', 'use_field_config']
        for param in field_params:
            if param in prepared_config:
                print(f"   ✅ {param}: {prepared_config[param]}")
            else:
                print(f"   ❌ {param}: 未找到")
        
        return True
        
    except ImportError as e:
        print(f"❌ 爬虫线程导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 爬虫线程集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_manager_integration():
    """测试配置管理器集成"""
    print("=" * 60)
    print("测试配置管理器集成")
    print("=" * 60)
    
    try:
        from gui.config_manager import GUIConfigManager
        print("✅ 配置管理器导入成功")
        
        # 创建配置管理器实例
        config_manager = GUIConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 测试配置准备
        test_gui_config = {
            'input_url': 'https://example.com',
            'content_selector': '.content',
            'field_config': {
                'use_field_config': True,
                'field_preset': 'social_media',
                'custom_field_list': ['title', 'likes', 'views']
            }
        }
        
        crawler_config = config_manager.prepare_crawler_config(test_gui_config)
        print(f"📊 爬虫配置准备成功，参数数量: {len(crawler_config)}")
        
        # 检查字段配置
        field_config = crawler_config.get('field_config', {})
        print(f"🔧 字段配置: {field_config}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 配置管理器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置管理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_end_to_end_flow():
    """测试端到端流程"""
    print("=" * 60)
    print("测试端到端流程")
    print("=" * 60)
    
    try:
        # 1. 导入所需模块
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        from core.field_config_manager import apply_field_preset
        
        # 2. 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 3. 创建主窗口
        window = CrawlerGUI()
        
        # 4. 模拟字段配置操作
        if hasattr(window, 'field_preset_combo'):
            # 模拟选择预设
            print("🔧 模拟选择字段预设...")
            
            # 应用基础预设
            success = apply_field_preset('basic')
            print(f"   基础预设应用: {'✅' if success else '❌'}")
            
            # 应用社交媒体预设
            success = apply_field_preset('social_media')
            print(f"   社交媒体预设应用: {'✅' if success else '❌'}")
        
        # 5. 获取配置
        config_data = window.get_config_from_gui()
        field_config = config_data.get('field_config', {})
        print(f"📊 最终字段配置: {field_config}")
        
        # 6. 测试配置转换
        crawler_config = window.config_manager.prepare_crawler_config(config_data)
        print(f"🚀 爬虫配置准备完成，包含 {len(crawler_config)} 个参数")
        
        print("✅ 端到端流程测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 端到端流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试GUI字段配置集成")
    print()
    
    tests = [
        ("字段配置模块导入测试", test_field_config_import),
        ("GUI集成测试", test_gui_integration),
        ("爬虫线程集成测试", test_crawler_thread_integration),
        ("配置管理器集成测试", test_config_manager_integration),
        ("端到端流程测试", test_end_to_end_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI字段配置集成成功。")
    else:
        print("⚠️ 部分测试失败，需要检查集成。")

if __name__ == "__main__":
    main()
