#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI配置管理模块
从 crawler_gui_2.py 中拆分出来的配置管理功能
"""

from config import manager


class GUIConfigManager:
    """GUI配置管理器，扩展基础配置管理器的功能"""
    
    def __init__(self):
        self.config_manager = manager.ConfigManager()
    
    def get_groups(self):
        """获取所有配置组"""
        return self.config_manager.get_groups()
    
    def get_group(self, group_name):
        """获取指定配置组"""
        return self.config_manager.get_group(group_name)
    
    def add_group(self, group_name, config_data):
        """添加配置组"""
        return self.config_manager.add_group(group_name, config_data)
    
    def delete_group(self, group_name):
        """删除配置组"""
        return self.config_manager.delete_group(group_name)
    
    def set_current_group(self, group_name):
        """设置当前配置组"""
        return self.config_manager.set_current_group(group_name)
    
    def get_current_group(self):
        """获取当前配置组"""
        return self.config_manager.get_current_group()
    
    def validate_config(self, config_data):
        """验证配置数据"""
        errors = []
        
        # 必填字段检查
        required_fields = ['input_url']
        for field in required_fields:
            if not config_data.get(field):
                errors.append(f"缺少必填字段: {field}")
        
        # URL格式检查
        input_url = config_data.get('input_url', '')
        if input_url and not (input_url.startswith('http://') or input_url.startswith('https://')):
            errors.append("输入URL格式不正确，应以http://或https://开头")
        
        # 数值字段检查
        try:
            max_pages = config_data.get('max_pages', '')
            if max_pages and int(max_pages) <= 0:
                errors.append("最大页数应大于0")
        except ValueError:
            errors.append("最大页数应为有效数字")
        
        return errors
    
    def prepare_crawler_config(self, gui_config):
        """将GUI配置转换为爬虫配置"""
        # 处理多选择器（统一使用复数形式）
        title_selectors = gui_config.get('title_selectors', [])
        if isinstance(title_selectors, str):
            title_selectors = [s.strip() for s in title_selectors.split(',') if s.strip()]

        date_selectors = gui_config.get('date_selectors', [])
        if isinstance(date_selectors, str):
            date_selectors = [s.strip() for s in date_selectors.split(',') if s.strip()]

        source_selectors = gui_config.get('source_selectors', [])
        if isinstance(source_selectors, str):
            source_selectors = [s.strip() for s in source_selectors.split(',') if s.strip()]
        
        content_selectors = []
        # 优先使用 content_selectors（多选择器），如果没有则使用 content_selector（单选择器）
        if gui_config.get('content_selectors'):
            # 如果是列表，直接使用
            if isinstance(gui_config['content_selectors'], list):
                content_selectors = gui_config['content_selectors']
            # 如果是字符串，按逗号分隔
            else:
                content_str = gui_config['content_selectors']
                content_selectors = [s.strip() for s in content_str.split(',') if s.strip()]
        elif gui_config.get('content_selector'):
            # 支持多个内容选择器，用逗号分隔
            content_str = gui_config['content_selector']
            content_selectors = [s.strip() for s in content_str.split(',') if s.strip()]

        # 如果没有内容选择器，使用默认值
        if not content_selectors:
            content_selectors = [
                ".article_cont",
                "div[class*='content']",
                "div[class*='article']",
                "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
                ".TRS_Editor",
                "div.zhengwen"
            ]
        
        # 构建爬虫配置
        crawler_config = {
            'input_url': gui_config.get('input_url', ''),
            'base_url': gui_config.get('base_url', ''),
            'max_pages': int(gui_config.get('max_pages', 5)) if gui_config.get('max_pages') else 5,
            'list_container_selector': gui_config.get('list_container_selector', '.main'),
            'article_item_selector': gui_config.get('article_item_selector', '.clearfix.ty_list li a'),

            # 多选择器支持（统一使用复数形式）
            'title_selectors': title_selectors,
            'date_selectors': date_selectors,
            'source_selectors': source_selectors,
            'content_selectors': content_selectors,
            'content_type': gui_config.get('content_type', 'CSS'),
            
            'page_suffix': gui_config.get('page_suffix', 'index_{n}.html'),
            'url_mode': gui_config.get('url_mode', 'absolute'),
            'browser_type': 'chromium',  # 使用Playwright的chromium
            'headless': gui_config.get('headless', True),
            
            # 新增功能
            'collect_links': gui_config.get('collect_links', True),
            'mode': gui_config.get('mode', 'balance'),
            'filters': gui_config.get('filters', []),
            'export_filename': gui_config.get('export_filename', ''),
            'classid': gui_config.get('classid', ''),
            'file_format': gui_config.get('file_format', 'CSV'),
            'max_workers': gui_config.get('max_workers', 5),
            'retry': gui_config.get('retry', 2),
            'interval': gui_config.get('interval', 0),
            
            # 动态翻页配置
            'pagination_config': gui_config.get('pagination_config', {}),

            # 字段配置
            'field_preset': gui_config.get('field_preset', ''),
            'custom_field_list': gui_config.get('custom_field_list', []),
            'user_custom_fields': gui_config.get('custom_fields', {}),  # 用户自定义字段
            'use_field_config': gui_config.get('use_field_config', False),
        }

        # 如果启用模组配置，检查是否需要覆盖GUI设置
        if crawler_config.get('use_module_config', True):
            input_url = crawler_config.get('input_url', '')
            if input_url:
                try:
                    # 尝试导入模组管理器
                    from modules.manager import match_module_for_url, get_config_for_url

                    # 检查是否有匹配的模组配置
                    module_name = match_module_for_url(input_url)
                    if module_name:
                        module_config = get_config_for_url(input_url)
                        if module_config:
                            # 模组配置优先，覆盖GUI设置
                            if 'collect_links' in module_config:
                                crawler_config['collect_links'] = module_config['collect_links']
                                print(f"🔧 模组配置覆盖: collect_links = {module_config['collect_links']} (模组: {module_name})")

                            # 其他可能需要覆盖的设置
                            for key in ['mode', 'retry', 'interval', 'headless']:
                                if key in module_config:
                                    crawler_config[key] = module_config[key]
                except ImportError:
                    # 模组管理器不可用，继续使用GUI设置
                    pass
                except Exception as e:
                    print(f"⚠️ 应用模组配置时出错: {e}")

        return crawler_config
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "input_url": "",
            "base_url": "",
            "max_pages": "5",
            "list_container_selector": ".main",
            "article_item_selector": ".clearfix.ty_list li a",
            "title_selectors": [],  # 使用复数形式，支持多选择器
            "content_selector": "",
            "content_selectors": [],  # 多选择器支持，优先级高于content_selector
            "content_type": "CSS",
            "date_selectors": ["div.xl_ly.fl > span"],  # 使用复数形式，支持多选择器
            "source_selectors": ["div.xl_ly.fl > span"],  # 使用复数形式，支持多选择器
            "page_suffix": "index_{n}.html",
            "url_mode": "absolute",
            "headless": True,
            "window_size": "",
            "page_load_strategy": "",
            "collect_links": True,
            "mode": "balance",
            "filters": [],
            "export_filename": "",
            "classid": "",
            "file_format": "CSV",
            "max_workers": 5,
            "retry": 2,
            "interval": 0,
            "use_module_config": True,  # 启用模组配置
            "pagination_config": {
                "enabled": False,
                "pagination_type": "禁用动态翻页"
            }
        }
    
    def merge_config_with_defaults(self, config_data):
        """将配置与默认值合并"""
        default_config = self.get_default_config()
        
        # 深度合并配置
        merged_config = default_config.copy()
        if config_data:
            merged_config.update(config_data)
            
            # 特殊处理嵌套字典
            if 'pagination_config' in config_data:
                merged_pagination = default_config['pagination_config'].copy()
                merged_pagination.update(config_data['pagination_config'])
                merged_config['pagination_config'] = merged_pagination
        
        return merged_config


class PaginationConfigHelper:
    """动态翻页配置助手"""
    
    @staticmethod
    def get_default_pagination_config():
        """获取默认翻页配置"""
        return {
            'enabled': False,
            'pagination_type': '禁用动态翻页',
            'next_button_selector': 'a.next:not(.lose)',
            'content_ready_selector': '',
            'timeout': 10000,
            'wait_after_click': 2000,
            'disabled_check': True,
            'scroll_container_selector': 'body',
            'scroll_step': 800,
            'scroll_delay': 2000,
            'load_indicator_selector': '',
            'scroll_timeout': 10000,
            'height_tolerance': 50,
            'load_element_pattern': '',
            'iframe_selector': '',
            'iframe_pagination_type': '点击翻页'
        }
    
    @staticmethod
    def validate_pagination_config(pagination_config):
        """验证翻页配置"""
        errors = []
        
        if not pagination_config.get('enabled', False):
            return errors  # 未启用翻页，无需验证
        
        pagination_type = pagination_config.get('pagination_type', '')
        
        if pagination_type == '点击翻页':
            if not pagination_config.get('next_button_selector'):
                errors.append("点击翻页模式需要设置下一页按钮选择器")
        
        elif pagination_type == '滚动翻页':
            if not pagination_config.get('scroll_container_selector'):
                errors.append("滚动翻页模式需要设置滚动容器选择器")
        
        elif pagination_type == 'iframe翻页':
            if not pagination_config.get('iframe_selector'):
                errors.append("iframe翻页模式需要设置iframe选择器")
        
        return errors
    
    @staticmethod
    def get_pagination_type_options():
        """获取翻页类型选项"""
        return ['禁用动态翻页', '点击翻页', '滚动翻页', 'iframe翻页']


if __name__ == "__main__":
    # 测试代码
    print("GUI配置管理模块已加载")
    print("主要功能:")
    print("- GUIConfigManager: GUI配置管理器")
    print("- PaginationConfigHelper: 动态翻页配置助手")
