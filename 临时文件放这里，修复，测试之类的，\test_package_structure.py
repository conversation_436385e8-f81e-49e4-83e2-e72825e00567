#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试包结构和导入
"""

import sys
import os
import importlib
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_package_structure():
    """测试包结构"""
    print("📦 测试包结构")
    print("="*50)
    
    expected_packages = {
        'core': ['__init__.py', 'crawler.py', 'failed_url_processor.py', 'PaginationHandler.py'],
        'gui': ['__init__.py', 'main_window.py', 'config_manager.py', 'crawler_thread.py', 'utils.py'],
        'ai': ['__init__.py', 'analyzer.py', 'helper.py', 'interactive.py'],
        'modules': ['__init__.py', 'manager.py', 'config_manager.py'],
        'testing': ['__init__.py', 'selectors_test.py', 'config.py'],
        'config': ['__init__.py', 'manager.py'],
        'utils': ['__init__.py', 'text_cleaner.py', 'playwright_config.py']
    }
    
    all_good = True
    
    for package, expected_files in expected_packages.items():
        if os.path.exists(package):
            print(f"✅ 包存在: {package}")
            
            for file in expected_files:
                file_path = os.path.join(package, file)
                if os.path.exists(file_path):
                    print(f"  ✅ 文件存在: {file_path}")
                else:
                    print(f"  ❌ 文件缺失: {file_path}")
                    all_good = False
        else:
            print(f"❌ 包缺失: {package}")
            all_good = False
    
    return all_good

def test_imports():
    """测试导入"""
    print("\n🔗 测试导入")
    print("="*50)
    
    test_cases = [
        # 核心模块
        ('core', 'from core import crawler'),
        ('core.crawler', 'from core.crawler import get_article_links_playwright'),
        ('core.failed_url_processor', 'from core.failed_url_processor import FailedUrlProcessor'),
        
        # GUI模块
        ('gui', 'from gui import main_window'),
        ('gui.main_window', 'from gui.main_window import CrawlerGUI'),
        ('gui.config_manager', 'from gui.config_manager import GUIConfigManager'),
        
        # AI模块
        ('ai', 'from ai import analyzer'),
        ('ai.analyzer', 'from ai.analyzer import AIAnalyzerWithTesting'),
        ('ai.helper', 'from ai.helper import EnhancedAIConfigManager'),
        
        # 模组模块
        ('modules', 'from modules import manager'),
        ('modules.manager', 'from modules.manager import ModuleManager'),
        
        # 测试模块
        ('testing', 'from testing import selectors_test'),
        ('testing.selectors_test', 'from testing.selectors_test import SelectorsTestManager'),
        
        # 配置模块
        ('config', 'from config import manager'),
        ('config.manager', 'from config.manager import ConfigManager'),
        
        # 工具模块
        ('utils', 'from utils import text_cleaner'),
        ('utils.text_cleaner', 'from utils.text_cleaner import normalize_date'),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for module_name, import_stmt in test_cases:
        try:
            exec(import_stmt)
            print(f"✅ {import_stmt}")
            success_count += 1
        except Exception as e:
            print(f"❌ {import_stmt}: {e}")
    
    print(f"\n导入测试结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def test_main_entry():
    """测试主入口"""
    print("\n🚀 测试主入口")
    print("="*50)
    
    if os.path.exists('main.py'):
        print("✅ main.py 存在")
        
        try:
            # 测试导入主模块
            import main
            print("✅ main.py 可以导入")
            return True
        except Exception as e:
            print(f"❌ main.py 导入失败: {e}")
            return False
    else:
        print("❌ main.py 不存在")
        return False

def test_circular_imports():
    """测试循环导入"""
    print("\n🔄 测试循环导入")
    print("="*50)
    
    # 重新加载所有模块以检测循环导入
    modules_to_test = [
        'core.crawler',
        'gui.main_window',
        'ai.analyzer',
        'modules.manager',
        'testing.selectors_test',
        'config.manager',
        'utils.text_cleaner'
    ]
    
    success_count = 0
    
    for module_name in modules_to_test:
        try:
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
            else:
                importlib.import_module(module_name)
            print(f"✅ {module_name} - 无循环导入")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name} - 可能存在循环导入: {e}")
    
    print(f"\n循环导入测试结果: {success_count}/{len(modules_to_test)} 成功")
    return success_count == len(modules_to_test)

def test_functionality():
    """测试基本功能"""
    print("\n⚙️ 测试基本功能")
    print("="*50)
    
    try:
        # 测试配置管理器
        from config.manager import ConfigManager
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 测试模组管理器
        from modules.manager import ModuleManager
        module_manager = ModuleManager()
        print("✅ 模组管理器创建成功")
        
        # 测试AI助手
        from ai.helper import EnhancedAIConfigManager
        ai_manager = EnhancedAIConfigManager()
        print("✅ AI助手创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def generate_usage_guide():
    """生成使用指南"""
    guide = """
📚 包结构使用指南

## 新的导入方式

### 核心功能
```python
from core import crawler
from core.crawler import get_article_links_playwright
from core.failed_url_processor import FailedUrlProcessor
```

### GUI界面
```python
from gui import main_window
from gui.main_window import CrawlerGUI
from gui.config_manager import GUIConfigManager
```

### AI分析
```python
from ai import analyzer, helper
from ai.analyzer import AIAnalyzerWithTesting
from ai.helper import EnhancedAIConfigManager
```

### 模组管理
```python
from modules import manager
from modules.manager import ModuleManager
```

### 测试功能
```python
from testing import selectors_test
from testing.selectors_test import SelectorsTestManager
```

### 配置管理
```python
from config import manager
from config.manager import ConfigManager
```

### 工具函数
```python
from utils import text_cleaner, playwright_config
from utils.text_cleaner import normalize_date
```

## 启动应用

### 方式1: 使用主入口
```bash
python main.py
```

### 方式2: 直接启动GUI
```python
from gui.main_window import main
main()
```

## 包结构优势

1. **模块化**: 功能清晰分离，易于维护
2. **可扩展**: 新功能可以独立开发
3. **可测试**: 每个包都可以独立测试
4. **可重用**: 包可以在其他项目中重用
"""
    
    with open('PACKAGE_USAGE_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("📖 使用指南已生成: PACKAGE_USAGE_GUIDE.md")

def main():
    """主测试函数"""
    print("🧪 包结构和导入测试")
    print("="*80)
    
    tests = [
        ("包结构", test_package_structure),
        ("导入测试", test_imports),
        ("主入口", test_main_entry),
        ("循环导入", test_circular_imports),
        ("基本功能", test_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*80)
    print("🎯 测试结果汇总")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！包结构重组成功！")
        
        # 生成使用指南
        generate_usage_guide()
        
        print("\n📦 新的包结构已就绪:")
        print("✅ 模块化设计完成")
        print("✅ 导入引用正确")
        print("✅ 无循环导入")
        print("✅ 基本功能正常")
        print("✅ 主入口可用")
        
        print("\n🚀 现在可以使用:")
        print("python main.py  # 启动应用")
        
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
