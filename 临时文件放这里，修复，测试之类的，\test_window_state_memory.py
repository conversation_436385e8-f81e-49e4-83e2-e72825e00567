#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI窗口状态记忆功能
验证窗口大小、位置、最大化状态的保存和恢复
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# 添加gui目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'gui'))

try:
    from window_state_manager import WindowStateManager, MultiWindowStateManager
    WINDOW_STATE_AVAILABLE = True
except ImportError as e:
    print(f"无法导入窗口状态管理器: {e}")
    WINDOW_STATE_AVAILABLE = False


class TestWindow(QMainWindow):
    """测试窗口类"""
    
    def __init__(self, window_name="TestWindow"):
        super().__init__()
        self.window_name = window_name
        self.setWindowTitle(f"窗口状态记忆测试 - {window_name}")
        
        # 初始化窗口状态管理器
        if WINDOW_STATE_AVAILABLE:
            self.window_state_manager = WindowStateManager("TestApp", window_name)
        
        # 设置默认几何信息
        default_geometry = (200, 200, 800, 600)
        self.setGeometry(*default_geometry)
        
        # 恢复窗口状态
        if WINDOW_STATE_AVAILABLE:
            self.window_state_manager.restore_window_state(self, default_geometry)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 窗口信息标签
        self.info_label = QLabel()
        self.update_info_label()
        layout.addWidget(self.info_label)
        
        # 测试按钮
        test_btn = QPushButton("更新窗口信息")
        test_btn.clicked.connect(self.update_info_label)
        layout.addWidget(test_btn)
        
        save_btn = QPushButton("手动保存窗口状态")
        save_btn.clicked.connect(self.save_state)
        layout.addWidget(save_btn)
        
        restore_btn = QPushButton("手动恢复窗口状态")
        restore_btn.clicked.connect(self.restore_state)
        layout.addWidget(restore_btn)
        
        clear_btn = QPushButton("清除保存的状态")
        clear_btn.clicked.connect(self.clear_state)
        layout.addWidget(clear_btn)
        
        maximize_btn = QPushButton("切换最大化状态")
        maximize_btn.clicked.connect(self.toggle_maximize)
        layout.addWidget(maximize_btn)
    
    def update_info_label(self):
        """更新窗口信息标签"""
        geometry = self.geometry()
        info_text = f"""
窗口名称: {self.window_name}
位置: ({geometry.x()}, {geometry.y()})
大小: {geometry.width()} x {geometry.height()}
最大化状态: {'是' if self.isMaximized() else '否'}
窗口状态管理器: {'可用' if WINDOW_STATE_AVAILABLE else '不可用'}
        """.strip()
        
        if WINDOW_STATE_AVAILABLE:
            has_saved = self.window_state_manager.has_saved_state()
            info_text += f"\n已保存状态: {'是' if has_saved else '否'}"
            
            if has_saved:
                saved_info = self.window_state_manager.get_saved_geometry()
                if saved_info:
                    info_text += f"\n保存的最大化状态: {'是' if saved_info.get('is_maximized') else '否'}"
        
        self.info_label.setText(info_text)
    
    def save_state(self):
        """手动保存窗口状态"""
        if WINDOW_STATE_AVAILABLE:
            success = self.window_state_manager.save_window_state(self)
            print(f"保存窗口状态: {'成功' if success else '失败'}")
            self.update_info_label()
        else:
            print("窗口状态管理器不可用")
    
    def restore_state(self):
        """手动恢复窗口状态"""
        if WINDOW_STATE_AVAILABLE:
            default_geometry = (200, 200, 800, 600)
            success = self.window_state_manager.restore_window_state(self, default_geometry)
            print(f"恢复窗口状态: {'成功' if success else '失败'}")
            self.update_info_label()
        else:
            print("窗口状态管理器不可用")
    
    def clear_state(self):
        """清除保存的状态"""
        if WINDOW_STATE_AVAILABLE:
            success = self.window_state_manager.clear_settings()
            print(f"清除保存状态: {'成功' if success else '失败'}")
            self.update_info_label()
        else:
            print("窗口状态管理器不可用")
    
    def toggle_maximize(self):
        """切换最大化状态"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
        self.update_info_label()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if WINDOW_STATE_AVAILABLE:
            self.window_state_manager.save_window_state(self)
            print(f"窗口 {self.window_name} 关闭时保存状态")
        event.accept()


def test_single_window():
    """测试单窗口状态记忆"""
    print("🧪 测试单窗口状态记忆功能")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow("SingleWindow")
    window.show()
    
    print("✅ 测试窗口已创建")
    print("📝 请手动测试以下功能:")
    print("   1. 调整窗口大小和位置")
    print("   2. 切换最大化状态")
    print("   3. 关闭窗口后重新运行程序")
    print("   4. 验证窗口状态是否正确恢复")
    
    return app.exec_()


def test_multi_window():
    """测试多窗口状态记忆"""
    print("🧪 测试多窗口状态记忆功能")
    print("=" * 50)
    
    if not WINDOW_STATE_AVAILABLE:
        print("❌ 窗口状态管理器不可用，跳过测试")
        return
    
    app = QApplication(sys.argv)
    
    # 创建多个测试窗口
    windows = []
    window_names = ["Window1", "Window2", "Window3"]
    
    for i, name in enumerate(window_names):
        window = TestWindow(name)
        # 设置不同的初始位置
        window.move(300 + i * 50, 300 + i * 50)
        window.show()
        windows.append(window)
    
    print(f"✅ 创建了 {len(windows)} 个测试窗口")
    print("📝 请手动测试以下功能:")
    print("   1. 分别调整各个窗口的大小和位置")
    print("   2. 设置不同的最大化状态")
    print("   3. 关闭所有窗口后重新运行程序")
    print("   4. 验证每个窗口的状态是否正确恢复")
    
    return app.exec_()


def test_window_state_manager():
    """测试窗口状态管理器功能"""
    print("🧪 测试窗口状态管理器功能")
    print("=" * 50)
    
    if not WINDOW_STATE_AVAILABLE:
        print("❌ 窗口状态管理器不可用，跳过测试")
        return False
    
    try:
        # 测试单窗口管理器
        manager = WindowStateManager("TestApp", "TestWindow")
        print("✅ 单窗口状态管理器创建成功")
        
        # 测试多窗口管理器
        multi_manager = MultiWindowStateManager("TestApp")
        manager1 = multi_manager.get_manager("Window1")
        manager2 = multi_manager.get_manager("Window2")
        
        print("✅ 多窗口状态管理器创建成功")
        print(f"   Window1管理器: {type(manager1).__name__}")
        print(f"   Window2管理器: {type(manager2).__name__}")
        
        # 测试设置清除
        manager.clear_settings()
        print("✅ 设置清除功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 窗口状态管理器测试失败: {e}")
        return False


def test_screen_compatibility():
    """测试屏幕兼容性"""
    print("🧪 测试屏幕兼容性")
    print("=" * 50)
    
    try:
        app = QApplication(sys.argv)
        
        # 获取屏幕信息
        desktop = app.desktop()
        screen_count = desktop.screenCount()
        primary_screen = desktop.primaryScreen()
        screen_geometry = desktop.screenGeometry()
        
        print(f"✅ 屏幕信息获取成功:")
        print(f"   屏幕数量: {screen_count}")
        print(f"   主屏幕: {primary_screen}")
        print(f"   屏幕分辨率: {screen_geometry.width()} x {screen_geometry.height()}")
        
        # 测试边界检查
        if WINDOW_STATE_AVAILABLE:
            manager = WindowStateManager("TestApp", "CompatibilityTest")
            
            # 模拟无效几何信息
            test_geometries = [
                (-1000, -1000, 800, 600),  # 完全在屏幕外
                (screen_geometry.width() + 100, 100, 800, 600),  # 超出右边界
                (100, screen_geometry.height() + 100, 800, 600),  # 超出下边界
                (100, 100, 50, 50),  # 窗口太小
                (100, 100, screen_geometry.width() * 3, 600),  # 窗口太宽
            ]
            
            print("✅ 边界检查测试:")
            for i, geometry in enumerate(test_geometries):
                # 创建模拟几何对象
                class MockGeometry:
                    def __init__(self, x, y, width, height):
                        self._x = x
                        self._y = y
                        self._width = width
                        self._height = height

                    def x(self):
                        return self._x

                    def y(self):
                        return self._y

                    def width(self):
                        return self._width

                    def height(self):
                        return self._height

                mock_geometry = MockGeometry(*geometry)
                is_valid = manager._is_geometry_valid(mock_geometry, screen_geometry)
                print(f"   几何信息 {i+1}: {'有效' if is_valid else '无效'} - {geometry}")
        
        return True
        
    except Exception as e:
        print(f"❌ 屏幕兼容性测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 GUI窗口状态记忆功能测试")
    print("=" * 80)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
    else:
        test_type = "single"
    
    if test_type == "single":
        return test_single_window()
    elif test_type == "multi":
        return test_multi_window()
    elif test_type == "manager":
        success = test_window_state_manager()
        return 0 if success else 1
    elif test_type == "screen":
        success = test_screen_compatibility()
        return 0 if success else 1
    else:
        print("用法:")
        print("  python test_window_state_memory.py single   # 测试单窗口")
        print("  python test_window_state_memory.py multi    # 测试多窗口")
        print("  python test_window_state_memory.py manager  # 测试管理器")
        print("  python test_window_state_memory.py screen   # 测试屏幕兼容性")
        return 1


if __name__ == "__main__":
    sys.exit(main())
