#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI工具模块
从 crawler_gui_2.py 中拆分出来的工具函数和样式
"""

from PyQt5.QtWidgets import QMessageBox, QInputDialog


def get_application_stylesheet():
    """返回应用样式表"""
    return """
        /* 全局字体设置 */
        * {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-size: 12px;
        }

        QComboBox {
            border-radius: 6px;
            padding: 5px;
            border: 1px solid #ccc;
            background-color: white;
            min-height: 20px;
            font-size: 12px;
        }
        
        QComboBox:hover {
            border-color: #0078d4;
        }
        
        QComboBox:focus {
            border-color: #0078d4;
            outline: none;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666;
            margin-right: 5px;
        }
        
        QLineEdit {
            border-radius: 6px;
            padding: 8px;
            border: 1px solid #ccc;
            background-color: white;
            font-size: 12px;
        }
        
        QLineEdit:hover {
            border-color: #0078d4;
        }
        
        QLineEdit:focus {
            border-color: #0078d4;
            outline: none;
        }
        
        QTextEdit {
            border-radius: 6px;
            padding: 8px;
            border: 1px solid #ccc;
            background-color: white;
            font-size: 12px;
        }
        
        QTextEdit:hover {
            border-color: #0078d4;
        }
        
        QTextEdit:focus {
            border-color: #0078d4;
            outline: none;
        }
        
        /* 数字输入框样式 */
        QSpinBox, QDoubleSpinBox {
            border-radius: 6px;
            padding: 6px;
            border: 1px solid #ccc;
            background-color: white;
            font-size: 12px;
            max-width: 80px;
            min-width: 60px;
        }

        QSpinBox:hover, QDoubleSpinBox:hover {
            border-color: #0078d4;
        }

        QSpinBox:focus, QDoubleSpinBox:focus {
            border-color: #0078d4;
            outline: none;
        }

        QPushButton {
            border-radius: 6px;
            padding: 8px 16px;
            border: 1px solid #0078d4;
            background-color: #0078d4;
            color: white;
            font-weight: bold;
            font-size: 12px;
            min-height: 20px;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
            border-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
            border-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #f0f0f0;
            border-color: #d0d0d0;
            color: #a0a0a0;
        }
        
        QGroupBox {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            font-weight: bold;
            font-size: 12px;
            border: 2px solid #cccccc;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 10px;
            background-color: #fafafa;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #333;
            background-color: #fafafa;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            border-radius: 6px;
            background-color: white;
        }
        
        QTabBar::tab {
            background-color: #f0f0f0;
            border: 1px solid #cccccc;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }
        
        QTabBar::tab:hover {
            background-color: #e6f3ff;
        }
        
        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 6px;
            text-align: center;
            background-color: #f0f0f0;
            height: 20px;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 5px;
        }

        QLabel {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            color: #333;
            font-size: 12px;
        }
        
        QSpinBox, QDoubleSpinBox {
            border-radius: 6px;
            padding: 5px;
            border: 1px solid #ccc;
            background-color: white;
            min-height: 20px;
        }
        
        QSpinBox:hover, QDoubleSpinBox:hover {
            border-color: #0078d4;
        }
        
        QSpinBox:focus, QDoubleSpinBox:focus {
            border-color: #0078d4;
            outline: none;
        }
        
        QCheckBox {
            spacing: 8px;
            font-size: 13px;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            border: 1px solid #ccc;
            background-color: white;
        }
        
        QCheckBox::indicator:hover {
            border-color: #0078d4;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }
        
        /* 特殊按钮样式 */
        #startButton {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        #startButton:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        #stopButton {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        
        #stopButton:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        
        #aiButton {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }
        
        #aiButton:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        
        #linkButton {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
            text-decoration: none;
        }
        
        #linkButton:hover {
            background-color: #5a32a3;
            border-color: #5a32a3;
        }
        
        #linkButton:pressed {
            opacity: 0.8;
        }
    """


def show_info_message(parent, title, message):
    """显示信息消息框"""
    QMessageBox.information(parent, title, message)


def show_warning_message(parent, title, message):
    """显示警告消息框"""
    QMessageBox.warning(parent, title, message)


def show_error_message(parent, title, message):
    """显示错误消息框"""
    QMessageBox.critical(parent, title, message)


def show_question_message(parent, title, message):
    """显示询问消息框"""
    reply = QMessageBox.question(
        parent, title, message,
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.No
    )
    return reply == QMessageBox.Yes


def get_text_input(parent, title, label, default_text=""):
    """获取文本输入"""
    text, ok = QInputDialog.getText(parent, title, label, text=default_text)
    return text if ok else None


def get_multiline_text_input(parent, title, label, default_text=""):
    """获取多行文本输入"""
    text, ok = QInputDialog.getMultiLineText(parent, title, label, text=default_text)
    return text if ok else None


def format_result_message(result):
    """格式化结果消息"""
    if isinstance(result, dict):
        total = result.get('total', 0)
        success = result.get('success', 0)
        failed = result.get('failed', 0)

        if 'error' in result:
            return f"任务出错: {result['error']}"

        # 基本结果信息
        message = f"爬取任务完成!\n总计: {total}篇\n成功: {success}篇\n失败: {failed}篇"

        # 添加失败URL文件信息
        if failed > 0:
            failed_file = result.get('failed_file', '')
            if failed_file:
                message += f"\n\n失败URL已保存到:\n{failed_file}"
            else:
                message += f"\n\n失败URL已保存到: failed_urls.csv"

            # 添加失败原因统计
            failed_reasons = result.get('failed_reasons', {})
            if failed_reasons:
                message += "\n\n失败原因统计:"
                for reason, count in failed_reasons.items():
                    message += f"\n• {reason}: {count}篇"

        # 添加处理时间信息
        if 'duration' in result:
            duration = result['duration']
            if duration >= 60:
                minutes = int(duration // 60)
                seconds = int(duration % 60)
                message += f"\n\n处理时间: {minutes}分{seconds}秒"
            else:
                message += f"\n\n处理时间: {duration:.1f}秒"

        return message
    else:
        return f"任务完成!\n结果: {result}"


def parse_progress_from_message(message):
    """从消息中解析进度信息"""
    try:
        # 查找形如 "进度: 5/10" 的模式
        import re
        match = re.search(r'进度:\s*(\d+)/(\d+)', message)
        if match:
            current = int(match.group(1))
            total = int(match.group(2))
            return current, total
    except:
        pass
    return None, None


def validate_url(url):
    """验证URL格式"""
    if not url:
        return False, "URL不能为空"
    
    if not (url.startswith('http://') or url.startswith('https://')):
        return False, "URL应以http://或https://开头"
    
    return True, ""


def validate_positive_integer(value, field_name):
    """验证正整数"""
    try:
        num = int(value)
        if num <= 0:
            return False, f"{field_name}应大于0"
        return True, ""
    except ValueError:
        return False, f"{field_name}应为有效数字"


def validate_selector(selector, field_name):
    """验证选择器格式（基础检查）"""
    if not selector:
        return True, ""  # 空选择器是允许的

    # 去除首尾空格
    selector = selector.strip()

    # 常见的CSS选择器模式
    css_patterns = [
        # ID选择器: #id
        selector.startswith('#'),
        # 类选择器: .class
        selector.startswith('.'),
        # 属性选择器: [attr], [attr=value]
        selector.startswith('[') and selector.endswith(']'),
        # 包含空格的复合选择器: div .class, .class > div
        ' ' in selector,
        # 包含逗号的多选择器: .class1, .class2
        ',' in selector,
        # 包含冒号的伪选择器: :first-child, :not()
        ':' in selector,
        # 包含点号的标签+类选择器: div.class, h1.title
        '.' in selector and not selector.startswith('.'),
        # 包含#的标签+ID选择器: div#id
        '#' in selector and not selector.startswith('#'),
        # 纯标签选择器: div, span, h1
        selector.replace('-', '').replace('_', '').isalpha(),
        # 包含>、+、~等组合符的选择器
        any(op in selector for op in ['>', '+', '~']),
        # XPath表达式（以//开头）
        selector.startswith('//'),
        # 包含括号的选择器（如伪类函数）
        '(' in selector and ')' in selector
    ]

    # 如果匹配任何一种模式，认为是有效的
    if any(css_patterns):
        return True, ""

    # 对于不匹配任何模式的选择器，给出警告而不是错误
    return True, f"{field_name}格式可能不常见，请确认是否正确"


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_basic_config(config):
        """验证基础配置"""
        errors = []
        
        # URL验证
        url_valid, url_error = validate_url(config.get('input_url', ''))
        if not url_valid:
            errors.append(f"输入URL: {url_error}")
        
        # 最大页数验证
        max_pages = config.get('max_pages', '')
        if max_pages:
            pages_valid, pages_error = validate_positive_integer(max_pages, "最大页数")
            if not pages_valid:
                errors.append(pages_error)
        
        return errors
    
    @staticmethod
    def validate_selectors(config):
        """验证选择器配置"""
        errors = []
        
        selectors_to_check = [
            ('list_container_selector', '列表容器选择器'),
            ('article_item_selector', '文章项选择器'),
            ('title_selector', '标题选择器'),
            ('content_selector', '内容选择器'),
            ('date_selector', '日期选择器'),
            ('source_selector', '来源选择器')
        ]
        
        for selector_key, selector_name in selectors_to_check:
            selector = config.get(selector_key, '')
            if selector:
                valid, error = validate_selector(selector, selector_name)
                if not valid:
                    errors.append(error)
        
        return errors


if __name__ == "__main__":
    # 测试代码
    print("GUI工具模块已加载")
    print("主要功能:")
    print("- 应用样式表")
    print("- 消息框工具函数")
    print("- 输入验证工具")
    print("- 配置验证器")
