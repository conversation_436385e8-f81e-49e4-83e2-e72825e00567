#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态翻页缓存和失败URL处理的修复
1. 测试动态翻页收集URL后的缓存保存功能
2. 测试失败URL处理的异步修复
"""

import asyncio
import sys
import os
import csv
import time

# 添加项目根目录到路径
sys.path.append('.')

async def test_dynamic_pagination_cache():
    """测试动态翻页的缓存功能"""
    print("=" * 70)
    print("🧪 测试动态翻页缓存功能修复")
    print("=" * 70)
    
    try:
        from core.crawler import crawl_articles_async
        
        # 模拟动态翻页收集到的文章数据
        mock_articles = [
            ("动态翻页文章1", "https://example.com/dynamic1.html", "articles", "动态翻页测试", "https://example.com/list.html", "test"),
            ("动态翻页文章2", "https://example.com/dynamic2.html", "articles", "动态翻页测试", "https://example.com/list.html", "test"),
            ("动态翻页文章3", "https://example.com/dynamic3.html", "articles", "动态翻页测试", "https://example.com/list.html", "test"),
        ]
        
        test_input_url = "https://example.com/list.html"
        test_config_group = "动态翻页缓存测试"
        
        def log_callback(message):
            print(f"📝 {message}")
        
        print("1. 测试动态翻页调用crawl_articles_async时的缓存保存...")
        print(f"   模拟文章数: {len(mock_articles)}")
        print(f"   测试URL: {test_input_url}")
        print(f"   配置组: {test_config_group}")
        
        # 模拟动态翻页调用crawl_articles_async
        result = await crawl_articles_async(
            all_articles=mock_articles,
            input_url=test_input_url,  # 修复：添加input_url参数
            config_group=test_config_group,  # 修复：添加config_group参数
            content_selectors=[".content", ".article-content"],
            title_selectors=[".title", "h1"],
            date_selectors=[".date", ".publish-time"],
            source_selectors=[".source", ".author"],
            collect_links=False,
            mode='safe',
            export_filename="dynamic_pagination_test",
            classid="dynamic_test",
            file_format="CSV",
            retry=1,
            interval=0.5,
            use_module_config=False,
            log_callback=log_callback
        )
        
        print(f"\n📊 处理结果:")
        print(f"   总计处理: {result.get('total', 0)} 篇文章")
        print(f"   成功处理: {result.get('success', 0)} 篇文章")
        print(f"   失败处理: {result.get('failed', 0)} 篇文章")
        
        print(f"\n2. 验证缓存文件是否生成...")
        
        # 检查缓存文件
        from core.crawler import check_url_cache_exists
        
        cache_exists, cache_path, cache_info = check_url_cache_exists(test_input_url, test_config_group)
        
        if cache_exists:
            print(f"   ✅ 缓存文件已生成: {cache_info['filename']}")
            print(f"   文件路径: {cache_path}")
            print(f"   文件大小: {cache_info['size']} bytes")
            print(f"   修改时间: {cache_info['modified_str']}")
            
            # 验证缓存内容
            from core.crawler import load_cached_urls_from_csv
            cached_urls = load_cached_urls_from_csv(test_input_url, test_config_group, log_callback)
            
            if len(cached_urls) == len(mock_articles):
                print(f"   ✅ 缓存内容正确: {len(cached_urls)} 个URL")
                
                print(f"\n📋 缓存的URL列表:")
                for i, url in enumerate(cached_urls, 1):
                    print(f"   {i}. {url}")
                    
                print("✅ 动态翻页缓存功能修复成功！")
                return True
            else:
                print(f"   ❌ 缓存内容异常: 预期 {len(mock_articles)} 个URL，实际 {len(cached_urls)} 个")
                return False
                
        else:
            print(f"   ❌ 缓存文件未生成")
            return False
        
    except Exception as e:
        print(f"❌ 动态翻页缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_failed_url_processor():
    """测试失败URL处理器的异步修复"""
    print("\n" + "=" * 70)
    print("🧪 测试失败URL处理器异步修复")
    print("=" * 70)
    
    try:
        # 创建测试失败URL文件
        test_failed_file = "test_output/test_failed_urls.csv"
        os.makedirs("test_output", exist_ok=True)
        
        # 创建失败URL测试数据
        failed_data = [
            ["failed_time", "failed_url", "title", "reason", "status"],
            ["2025-07-16 16:00:00", "https://example.com/test1.html", "测试文章1", "网络超时", "待重试"],
            ["2025-07-16 16:01:00", "https://example.com/test2.html", "测试文章2", "内容为空", "待重试"],
            ["2025-07-16 16:02:00", "https://example.com/test3.html", "测试文章3", "解析错误", "待重试"],
        ]
        
        with open(test_failed_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(failed_data)
        
        print(f"1. 创建测试失败URL文件: {test_failed_file}")
        print(f"   失败URL数量: {len(failed_data) - 1}")
        
        print(f"\n2. 测试异步失败URL处理器...")
        
        from core.failed_url_processor import process_failed_csv_via_crawler
        
        def log_callback(message):
            print(f"📝 {message}")
        
        def progress_callback(current, total):
            print(f"📊 进度: {current}/{total} ({int(current/total*100)}%)")
        
        stop_flag = False
        def stop_check():
            return stop_flag
        
        # 测试异步处理
        result = await process_failed_csv_via_crawler(
            failed_csv_path=test_failed_file,
            save_dir="test_output",
            export_filename="failed_url_retry_test",
            file_format="CSV",
            classid="test",
            retry=1,
            interval=0.5,
            max_workers=2,
            progress_callback=progress_callback,
            log_callback=log_callback,
            stop_flag=stop_check,
            mode="safe"
        )
        
        print(f"\n📊 失败URL处理结果:")
        print(f"   总计处理: {result.get('total', 0)} 个URL")
        print(f"   成功处理: {result.get('success', 0)} 个URL")
        print(f"   失败处理: {result.get('failed', 0)} 个URL")
        
        if result.get('total', 0) == 3:  # 预期处理3个失败URL
            print("✅ 失败URL处理器异步修复成功！")
            return True
        else:
            print(f"❌ 失败URL处理器异常: 预期处理3个URL，实际处理{result.get('total', 0)}个")
            return False
        
    except Exception as e:
        print(f"❌ 失败URL处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_thread_config():
    """测试GUI线程配置修复"""
    print("\n" + "=" * 70)
    print("🧪 测试GUI线程配置修复")
    print("=" * 70)
    
    try:
        # 检查GUI爬虫线程中的修复
        print("1. 检查GUI爬虫线程中的input_url和config_group参数...")
        
        with open('gui/crawler_thread.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "input_url=self.config.get('input_url', '')" in content:
            print("   ✅ 找到input_url参数传递")
        else:
            print("   ❌ 未找到input_url参数传递")
            return False
            
        if "config_group=self.config.get('config_group', 'default')" in content:
            print("   ✅ 找到config_group参数传递")
        else:
            print("   ❌ 未找到config_group参数传递")
            return False
        
        print("2. 检查GUI主窗口中的异步失败URL处理修复...")
        
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
            
        if "asyncio.run(async_process())" in main_content:
            print("   ✅ 找到异步处理修复")
        else:
            print("   ❌ 未找到异步处理修复")
            return False
            
        if "async def async_process():" in main_content:
            print("   ✅ 找到异步函数定义")
        else:
            print("   ❌ 未找到异步函数定义")
            return False
        
        print("✅ GUI配置修复验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ GUI配置修复测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始缓存和失败URL处理修复验证测试")
    
    # 创建测试输出目录
    os.makedirs("test_output", exist_ok=True)
    
    # 执行所有测试
    test1_result = await test_dynamic_pagination_cache()
    test2_result = await test_failed_url_processor()
    test3_result = test_gui_thread_config()
    
    print("\n" + "=" * 70)
    print("📊 测试总结")
    print("=" * 70)
    print(f"动态翻页缓存修复: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"失败URL处理异步修复: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"GUI配置修复验证: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    all_passed = test1_result and test2_result and test3_result
    
    if all_passed:
        print(f"\n🎉 所有修复验证通过！")
        print(f"\n💡 修复总结:")
        print(f"   1. 动态翻页现在会正确传递input_url和config_group参数，缓存功能正常")
        print(f"   2. 失败URL处理现在使用正确的异步调用方式，不会卡住GUI")
        print(f"   3. GUI线程配置已修复，支持完整的缓存和异步处理功能")
    else:
        print(f"\n❌ 部分修复验证失败，请检查相关代码。")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
