#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选择器测试对话框
测试字段选择器是否能正确提取数据
"""

import sys
import os
import asyncio
import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QProgressBar,
                            QGroupBox, QScrollArea, QWidget, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

class SelectorTestThread(QThread):
    """选择器测试线程"""
    
    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(str)
    
    def __init__(self, test_url, field_selectors):
        super().__init__()
        self.test_url = test_url
        self.field_selectors = field_selectors
    
    def run(self):
        """运行测试"""
        try:
            # 运行异步测试
            result = asyncio.run(self.test_selectors_async())
            self.result_signal.emit(result)
        except Exception as e:
            self.error_signal.emit(str(e))
    
    async def test_selectors_async(self):
        """异步测试选择器"""
        try:
            from playwright.async_api import async_playwright
            from bs4 import BeautifulSoup
            
            self.progress_signal.emit("正在启动浏览器...")
            
            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                self.progress_signal.emit(f"正在访问页面: {self.test_url}")
                
                # 访问页面
                await page.goto(self.test_url, timeout=30000)
                await page.wait_for_load_state('networkidle', timeout=10000)
                
                # 获取页面内容
                content = await page.content()
                soup = BeautifulSoup(content, 'html.parser')
                
                self.progress_signal.emit("正在测试选择器...")
                
                # 测试每个字段的选择器
                results = {}
                
                for field_name, selectors in self.field_selectors.items():
                    field_results = []
                    
                    for i, selector in enumerate(selectors):
                        try:
                            # 测试选择器
                            elements = soup.select(selector)
                            
                            if elements:
                                # 获取前3个元素的文本
                                texts = []
                                for elem in elements[:3]:
                                    text = elem.get_text(strip=True)
                                    if text:
                                        texts.append(text)
                                
                                field_results.append({
                                    'selector': selector,
                                    'status': 'success',
                                    'count': len(elements),
                                    'samples': texts[:3],
                                    'priority': i + 1
                                })
                            else:
                                field_results.append({
                                    'selector': selector,
                                    'status': 'no_match',
                                    'count': 0,
                                    'samples': [],
                                    'priority': i + 1
                                })
                                
                        except Exception as e:
                            field_results.append({
                                'selector': selector,
                                'status': 'error',
                                'error': str(e),
                                'count': 0,
                                'samples': [],
                                'priority': i + 1
                            })
                    
                    results[field_name] = field_results
                
                await browser.close()
                
                self.progress_signal.emit("测试完成")
                return results
                
        except Exception as e:
            raise Exception(f"测试选择器时出错: {e}")


class SelectorTestDialog(QDialog):
    """选择器测试对话框"""
    
    def __init__(self, parent=None, test_url="", field_selectors=None):
        super().__init__(parent)
        self.setWindowTitle("选择器测试结果")
        self.setModal(True)
        self.resize(900, 700)
        
        self.test_url = test_url
        self.field_selectors = field_selectors or {}
        
        self.setup_ui()
        self.start_test()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()
        
        # 测试信息
        info_group = QGroupBox("测试信息")
        info_layout = QVBoxLayout()
        
        url_label = QLabel(f"测试URL: {self.test_url}")
        url_label.setWordWrap(True)
        info_layout.addWidget(url_label)
        
        field_count_label = QLabel(f"测试字段数量: {len(self.field_selectors)}")
        info_layout.addWidget(field_count_label)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 不确定进度
        layout.addWidget(self.progress_bar)
        
        # 进度信息
        self.progress_label = QLabel("准备开始测试...")
        layout.addWidget(self.progress_label)
        
        # 结果显示区域
        self.result_area = QScrollArea()
        self.result_widget = QWidget()
        self.result_layout = QVBoxLayout()
        self.result_widget.setLayout(self.result_layout)
        self.result_area.setWidget(self.result_widget)
        self.result_area.setWidgetResizable(True)
        layout.addWidget(self.result_area)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        # 重新测试按钮
        retest_btn = QPushButton("重新测试")
        retest_btn.clicked.connect(self.start_test)
        button_layout.addWidget(retest_btn)
        
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def start_test(self):
        """开始测试"""
        # 清空结果区域
        for i in reversed(range(self.result_layout.count())):
            child = self.result_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 显示进度
        self.progress_bar.setVisible(True)
        self.progress_label.setText("正在启动测试...")
        
        # 启动测试线程
        self.test_thread = SelectorTestThread(self.test_url, self.field_selectors)
        self.test_thread.result_signal.connect(self.show_results)
        self.test_thread.error_signal.connect(self.show_error)
        self.test_thread.progress_signal.connect(self.update_progress)
        self.test_thread.start()
    
    def update_progress(self, message):
        """更新进度信息"""
        self.progress_label.setText(message)
    
    def show_error(self, error_message):
        """显示错误"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText(f"测试失败: {error_message}")
        
        error_label = QLabel(f"❌ 测试失败: {error_message}")
        error_label.setStyleSheet("color: red; font-weight: bold; padding: 10px;")
        error_label.setWordWrap(True)
        self.result_layout.addWidget(error_label)
    
    def show_results(self, results):
        """显示测试结果"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText("测试完成")
        
        if not results:
            no_result_label = QLabel("没有测试结果")
            self.result_layout.addWidget(no_result_label)
            return
        
        # 显示每个字段的测试结果
        for field_name, field_results in results.items():
            self.create_field_result_group(field_name, field_results)
    
    def create_field_result_group(self, field_name, field_results):
        """创建字段结果组"""
        # 计算成功的选择器数量
        success_count = sum(1 for r in field_results if r['status'] == 'success')
        total_count = len(field_results)
        
        # 确定状态颜色
        if success_count > 0:
            status_color = "green"
            status_text = f"✅ {success_count}/{total_count} 个选择器有效"
        else:
            status_color = "red"
            status_text = f"❌ 0/{total_count} 个选择器有效"
        
        group = QGroupBox(f"{field_name} - {status_text}")
        group.setStyleSheet(f"QGroupBox::title {{ color: {status_color}; font-weight: bold; }}")
        layout = QVBoxLayout()
        
        # 显示每个选择器的结果
        for result in field_results:
            selector_widget = self.create_selector_result_widget(result)
            layout.addWidget(selector_widget)
        
        group.setLayout(layout)
        self.result_layout.addWidget(group)
    
    def create_selector_result_widget(self, result):
        """创建选择器结果控件"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 选择器信息
        selector = result['selector']
        status = result['status']
        priority = result['priority']
        
        # 状态图标和文本
        if status == 'success':
            status_icon = "✅"
            status_color = "green"
            count = result['count']
            status_text = f"找到 {count} 个元素"
        elif status == 'no_match':
            status_icon = "⚠️"
            status_color = "orange"
            status_text = "未找到匹配元素"
        else:  # error
            status_icon = "❌"
            status_color = "red"
            error = result.get('error', '未知错误')
            status_text = f"错误: {error}"
        
        # 选择器标题
        title_label = QLabel(f"{status_icon} 优先级 {priority}: {selector}")
        title_label.setStyleSheet(f"color: {status_color}; font-weight: bold;")
        layout.addWidget(title_label)
        
        # 状态信息
        status_label = QLabel(f"   {status_text}")
        status_label.setStyleSheet(f"color: {status_color}; margin-left: 20px;")
        layout.addWidget(status_label)
        
        # 示例数据
        if status == 'success' and result.get('samples'):
            samples_label = QLabel("   示例数据:")
            samples_label.setStyleSheet("color: #666; margin-left: 20px;")
            layout.addWidget(samples_label)
            
            for i, sample in enumerate(result['samples'][:3]):
                sample_label = QLabel(f"     {i+1}. {sample}")
                sample_label.setStyleSheet("color: #888; margin-left: 40px;")
                sample_label.setWordWrap(True)
                layout.addWidget(sample_label)
        
        widget.setLayout(layout)
        return widget


if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 测试数据
    test_selectors = {
        "likes": [".like-count", ".praise-num"],
        "views": [".view-count", ".read-num"]
    }
    
    dialog = SelectorTestDialog(None, "https://example.com", test_selectors)
    dialog.show()
    sys.exit(app.exec_())
