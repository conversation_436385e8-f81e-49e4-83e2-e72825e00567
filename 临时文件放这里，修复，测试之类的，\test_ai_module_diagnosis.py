#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模块诊断测试
全面检查AI模块的功能和集成状态
"""

import sys
import os
import asyncio
import traceback
import json
from typing import Dict, Any

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    print("="*50)
    
    results = {}
    
    # 测试AI模块导入
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        results['ai_analyzer'] = "✅ 成功"
        print("✅ ai.analyzer.AIAnalyzerWithTesting 导入成功")
    except Exception as e:
        results['ai_analyzer'] = f"❌ 失败: {e}"
        print(f"❌ ai.analyzer.AIAnalyzerWithTesting 导入失败: {e}")
    
    # 测试AI助手导入
    try:
        from ai.helper import EnhancedAIConfigManager, LLMConfigDialog
        results['ai_helper'] = "✅ 成功"
        print("✅ ai.helper 导入成功")
    except Exception as e:
        results['ai_helper'] = f"❌ 失败: {e}"
        print(f"❌ ai.helper 导入失败: {e}")
    
    # 测试交互式AI导入
    try:
        from ai.interactive import InteractiveAIAnalyzer
        results['ai_interactive'] = "✅ 成功"
        print("✅ ai.interactive 导入成功")
    except Exception as e:
        results['ai_interactive'] = f"❌ 失败: {e}"
        print(f"❌ ai.interactive 导入失败: {e}")
    
    # 测试依赖模块导入
    try:
        from testing.selectors_test import SelectorsTestManager
        results['selectors_test'] = "✅ 成功"
        print("✅ testing.selectors_test 导入成功")
    except Exception as e:
        results['selectors_test'] = f"❌ 失败: {e}"
        print(f"❌ testing.selectors_test 导入失败: {e}")
    
    # 测试核心模块导入
    try:
        from core import crawler
        results['core_crawler'] = "✅ 成功"
        print("✅ core.crawler 导入成功")
    except Exception as e:
        results['core_crawler'] = f"❌ 失败: {e}"
        print(f"❌ core.crawler 导入失败: {e}")
    
    # 测试第三方依赖
    try:
        from openai import OpenAI
        results['openai'] = "✅ 成功"
        print("✅ openai 导入成功")
    except Exception as e:
        results['openai'] = f"❌ 失败: {e}"
        print(f"❌ openai 导入失败: {e}")
    
    try:
        from playwright.async_api import async_playwright
        results['playwright'] = "✅ 成功"
        print("✅ playwright 导入成功")
    except Exception as e:
        results['playwright'] = f"❌ 失败: {e}"
        print(f"❌ playwright 导入失败: {e}")
    
    return results

def test_ai_config():
    """测试AI配置"""
    print("\n🔧 测试AI配置...")
    print("="*50)
    
    results = {}
    
    # 检查配置文件
    config_file = "configs/ai/llm_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            results['config_file'] = "✅ 存在"
            print("✅ LLM配置文件存在")
            
            # 检查必要字段
            required_fields = ['api_key', 'base_url', 'model', 'enable_ai']
            for field in required_fields:
                if field in config:
                    results[f'config_{field}'] = "✅ 存在"
                    print(f"✅ 配置字段 {field}: {config[field]}")
                else:
                    results[f'config_{field}'] = "❌ 缺失"
                    print(f"❌ 配置字段 {field} 缺失")
            
        except Exception as e:
            results['config_file'] = f"❌ 读取失败: {e}"
            print(f"❌ 读取配置文件失败: {e}")
    else:
        results['config_file'] = "❌ 不存在"
        print("❌ LLM配置文件不存在")
    
    return results

def test_ai_analyzer_creation():
    """测试AI分析器创建"""
    print("\n🤖 测试AI分析器创建...")
    print("="*50)
    
    results = {}
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        results['analyzer_creation'] = "✅ 成功"
        print("✅ AI分析器创建成功")
        
        # 检查分析器属性
        if hasattr(analyzer, 'test_manager'):
            results['test_manager'] = "✅ 存在"
            print("✅ test_manager 属性存在")
        else:
            results['test_manager'] = "❌ 缺失"
            print("❌ test_manager 属性缺失")
        
        if hasattr(analyzer, 'retry_strategies'):
            results['retry_strategies'] = "✅ 存在"
            print(f"✅ retry_strategies 存在，包含 {len(analyzer.retry_strategies)} 个策略")
        else:
            results['retry_strategies'] = "❌ 缺失"
            print("❌ retry_strategies 属性缺失")
        
    except Exception as e:
        results['analyzer_creation'] = f"❌ 失败: {e}"
        print(f"❌ AI分析器创建失败: {e}")
        traceback.print_exc()
    
    return results

def test_ai_manager_creation():
    """测试AI管理器创建"""
    print("\n📋 测试AI管理器创建...")
    print("="*50)
    
    results = {}
    
    try:
        from ai.helper import EnhancedAIConfigManager
        manager = EnhancedAIConfigManager()
        results['manager_creation'] = "✅ 成功"
        print("✅ AI管理器创建成功")
        
        # 测试关键方法
        methods_to_test = ['check_llm_config', 'is_running', 'check_module_exists']
        for method in methods_to_test:
            if hasattr(manager, method):
                results[f'method_{method}'] = "✅ 存在"
                print(f"✅ 方法 {method} 存在")
            else:
                results[f'method_{method}'] = "❌ 缺失"
                print(f"❌ 方法 {method} 缺失")
        
        # 测试LLM配置检查
        try:
            llm_status = manager.check_llm_config()
            results['llm_config_check'] = f"✅ 成功: {llm_status}"
            print(f"✅ LLM配置检查: {llm_status}")
        except Exception as e:
            results['llm_config_check'] = f"❌ 失败: {e}"
            print(f"❌ LLM配置检查失败: {e}")
        
    except Exception as e:
        results['manager_creation'] = f"❌ 失败: {e}"
        print(f"❌ AI管理器创建失败: {e}")
        traceback.print_exc()
    
    return results

def test_core_functions():
    """测试核心函数"""
    print("\n⚙️ 测试核心函数...")
    print("="*50)
    
    results = {}
    
    try:
        from core import crawler
        
        # 检查关键函数
        functions_to_check = ['launch_browser', 'get_article_links_playwright', 'get_full_link']
        for func_name in functions_to_check:
            if hasattr(crawler, func_name):
                results[f'function_{func_name}'] = "✅ 存在"
                print(f"✅ 函数 {func_name} 存在")
            else:
                results[f'function_{func_name}'] = "❌ 缺失"
                print(f"❌ 函数 {func_name} 缺失")
        
    except Exception as e:
        results['core_functions'] = f"❌ 失败: {e}"
        print(f"❌ 核心函数检查失败: {e}")
    
    return results

async def test_ai_analyzer_basic_functionality():
    """测试AI分析器基础功能"""
    print("\n🧪 测试AI分析器基础功能...")
    print("="*50)
    
    results = {}
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        
        # 测试HTML清理功能
        test_html = "<html><head><script>test</script></head><body><p>Test content</p></body></html>"
        cleaned = analyzer._clean_html(test_html)
        if "script" not in cleaned and "Test content" in cleaned:
            results['html_cleaning'] = "✅ 成功"
            print("✅ HTML清理功能正常")
        else:
            results['html_cleaning'] = "❌ 失败"
            print("❌ HTML清理功能异常")
        
        # 测试AI结果解析
        test_ai_result = '{"title_selectors": ["h1", ".title"], "content_selectors": [".content"]}'
        parsed = analyzer._parse_ai_result(test_ai_result)
        if isinstance(parsed, dict) and 'title_selectors' in parsed:
            results['ai_result_parsing'] = "✅ 成功"
            print("✅ AI结果解析功能正常")
        else:
            results['ai_result_parsing'] = "❌ 失败"
            print("❌ AI结果解析功能异常")
        
    except Exception as e:
        results['basic_functionality'] = f"❌ 失败: {e}"
        print(f"❌ AI分析器基础功能测试失败: {e}")
        traceback.print_exc()
    
    return results

def generate_diagnosis_report(all_results: Dict[str, Dict[str, str]]):
    """生成诊断报告"""
    print("\n📊 AI模块诊断报告")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        print(f"\n{category}:")
        for test_name, result in results.items():
            total_tests += 1
            if result.startswith("✅"):
                passed_tests += 1
            print(f"  {test_name}: {result}")
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"\n总体测试结果: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    # 识别主要问题
    print("\n🔍 主要问题识别:")
    issues = []
    
    if 'basic_imports' in all_results:
        for test, result in all_results['basic_imports'].items():
            if result.startswith("❌"):
                issues.append(f"导入问题: {test} - {result}")
    
    if 'ai_config' in all_results:
        for test, result in all_results['ai_config'].items():
            if result.startswith("❌"):
                issues.append(f"配置问题: {test} - {result}")
    
    if issues:
        for issue in issues:
            print(f"  ❌ {issue}")
    else:
        print("  ✅ 未发现明显问题")
    
    return {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'issues': issues
    }

async def main():
    """主函数"""
    print("🚀 AI模块全面诊断测试")
    print("="*60)
    
    all_results = {}
    
    # 运行所有测试
    all_results['basic_imports'] = test_basic_imports()
    all_results['ai_config'] = test_ai_config()
    all_results['ai_analyzer_creation'] = test_ai_analyzer_creation()
    all_results['ai_manager_creation'] = test_ai_manager_creation()
    all_results['core_functions'] = test_core_functions()
    all_results['basic_functionality'] = await test_ai_analyzer_basic_functionality()
    
    # 生成报告
    report = generate_diagnosis_report(all_results)
    
    # 保存结果
    try:
        with open('ai_module_diagnosis_report.json', 'w', encoding='utf-8') as f:
            json.dump({
                'results': all_results,
                'summary': report
            }, f, ensure_ascii=False, indent=2)
        print(f"\n📄 诊断报告已保存到: ai_module_diagnosis_report.json")
    except Exception as e:
        print(f"\n❌ 保存诊断报告失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
