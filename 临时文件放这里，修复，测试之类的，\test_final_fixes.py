#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证测试
验证所有问题都已解决
"""

import sys
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_module_manager_methods():
    """测试模组管理器的所有方法"""
    print("🔧 测试模组管理器方法")
    print("="*50)
    
    try:
        from modules.manager import module_manager
        
        # 测试 list_modules
        modules = module_manager.list_modules()
        print(f"✅ list_modules(): {len(modules)} 个模组")
        
        # 测试 load_modules
        module_manager.load_modules()
        print("✅ load_modules(): 重新加载成功")
        
        # 测试 save_modules
        module_manager.save_modules()
        print("✅ save_modules(): 保存成功")
        
        # 测试 match_module_for_url
        test_url = "http://www.zhzx.gov.cn/zwhgz/jjwkjw/"
        matched = module_manager.match_module_for_url(test_url)
        print(f"✅ match_module_for_url(): {matched}")
        
        # 测试便捷函数
        from modules.manager import match_module_for_url, get_config_for_url
        matched_func = match_module_for_url(test_url)
        config_func = get_config_for_url(test_url)
        print(f"✅ 便捷函数 match_module_for_url(): {matched_func}")
        print(f"✅ 便捷函数 get_config_for_url(): {len(config_func)} 个配置项")
        
        return True
        
    except Exception as e:
        print(f"❌ 模组管理器测试失败: {e}")
        return False

def test_gui_module_operations():
    """测试GUI模组操作相关功能"""
    print("\n🖥️ 测试GUI模组操作")
    print("="*50)
    
    try:
        # 测试导入
        from gui.main_window import CrawlerGUI
        print("✅ GUI主窗口类导入成功")
        
        # 测试模组管理器导入
        from modules.manager import module_manager
        print("✅ 模组管理器导入成功")
        
        # 模拟GUI操作测试
        modules = module_manager.list_modules()
        print(f"✅ 模组列表获取: {modules}")
        
        # 测试模组信息获取
        if modules:
            module_info = module_manager.get_module_info(modules[0])
            print(f"✅ 模组信息获取: {modules[0]} - {module_info.get('description', '无描述')}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI模组操作测试失败: {e}")
        return False

def test_ai_analyzer():
    """测试AI分析器"""
    print("\n🤖 测试AI分析器")
    print("="*50)
    
    try:
        from ai.analyzer import AIAnalyzerWithTesting
        analyzer = AIAnalyzerWithTesting()
        print("✅ AI分析器创建成功")
        
        # 测试导入是否正确
        from ai import analyzer as ai_module
        print("✅ AI模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ AI分析器测试失败: {e}")
        return False

def test_config_managers():
    """测试配置管理器"""
    print("\n⚙️ 测试配置管理器")
    print("="*50)
    
    try:
        # 测试统一配置管理器
        from config.unified_manager import unified_config_manager
        app_config = unified_config_manager.get_app_config()
        module_configs = unified_config_manager.get_module_configs()
        print(f"✅ 统一配置管理器: 应用配置 {len(app_config)} 项, 模组配置 {len(module_configs)} 项")
        
        # 测试原配置管理器
        from config.manager import ConfigManager
        config_manager = ConfigManager()
        groups = config_manager.get_groups()
        print(f"✅ 原配置管理器: {len(groups)} 个配置组")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_core_modules():
    """测试核心模块"""
    print("\n🕷️ 测试核心模块")
    print("="*50)
    
    try:
        # 测试爬虫模块
        from core import crawler
        print("✅ 核心爬虫模块导入成功")
        
        # 测试失败URL处理器
        from core.failed_url_processor import FailedUrlProcessor
        processor = FailedUrlProcessor()
        print("✅ 失败URL处理器创建成功")
        
        # 测试分页处理器类
        from core.PaginationHandler import PaginationHandler
        print("✅ 分页处理器类导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心模块测试失败: {e}")
        return False

def test_import_compatibility():
    """测试导入兼容性"""
    print("\n🔗 测试导入兼容性")
    print("="*50)
    
    try:
        # 测试新的导入方式
        from modules.manager import module_manager
        from config.manager import ConfigManager
        from ai.analyzer import AIAnalyzerWithTesting
        print("✅ 新导入方式正常")
        
        # 测试兼容性导入
        import crawler as old_crawler
        import module_manager as old_module_manager
        print("✅ 兼容性导入正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 最终修复验证测试")
    print("="*80)
    
    tests = [
        ("模组管理器方法", test_module_manager_methods),
        ("GUI模组操作", test_gui_module_operations),
        ("AI分析器", test_ai_analyzer),
        ("配置管理器", test_config_managers),
        ("核心模块", test_core_modules),
        ("导入兼容性", test_import_compatibility)
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "="*80)
    print("🎯 最终测试结果汇总")
    print("="*80)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("\n🎉 所有修复验证通过！")
        print("✅ 模组管理器 load_modules 方法正常")
        print("✅ 模组管理器 save_modules 方法正常")
        print("✅ 模组管理器 list_modules 方法正常")
        print("✅ 'core' 未定义错误已解决")
        print("✅ 配置文件路径引用正确")
        print("✅ 所有功能模块正常工作")
        print("\n🚀 应用现在可以完全正常使用！")
    else:
        print(f"\n⚠️ 仍有 {len(tests) - passed} 个问题需要解决")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
