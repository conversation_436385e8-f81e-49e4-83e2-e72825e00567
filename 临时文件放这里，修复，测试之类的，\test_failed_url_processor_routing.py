#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 failed_url_processor.py 的新路由架构
验证 all_articles 路由机制是否正常工作
"""

import asyncio
import os
import sys
import logging
import tempfile
import csv
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_failed_csv(file_path: str, test_urls: list):
    """创建测试用的失败URL CSV文件"""
    with open(file_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['failed_time', 'failed_url', 'title', 'reason', 'status'])
        
        for i, url in enumerate(test_urls):
            writer.writerow([
                '2024-01-01 12:00:00',
                url,
                f'测试文章{i+1}',
                '测试重试',
                '待重试'
            ])

async def test_convert_to_all_articles():
    """测试 convert_to_all_articles 方法"""
    print("=" * 60)
    print("测试 convert_to_all_articles 方法")
    print("=" * 60)
    
    try:
        from core.failed_url_processor import FailedUrlProcessor
        
        processor = FailedUrlProcessor()
        
        # 测试数据
        failed_urls = [
            {
                'failed_url': 'https://example.com/article1',
                'title': '测试文章1',
                'reason': '测试',
                'status': '待重试'
            },
            {
                'failed_url': 'https://example.com/article2',
                'title': '测试文章2',
                'reason': '测试',
                'status': '待重试'
            },
            {
                'failed_url': 'https://example.com/article3',
                'title': '',  # 空标题测试
                'reason': '测试',
                'status': '待重试'
            }
        ]
        
        # 转换为 all_articles 格式
        all_articles = processor.convert_to_all_articles(failed_urls, "test_articles", "test_classid")
        
        print(f"✅ 转换成功，共 {len(all_articles)} 个条目")
        for i, article in enumerate(all_articles):
            print(f"   {i+1}. {article}")
        
        # 验证格式
        expected_format = 6  # (title, href, save_dir, page_title, page_url, classid)
        for article in all_articles:
            if len(article) != expected_format:
                print(f"❌ 格式错误: 期望 {expected_format} 个字段，实际 {len(article)} 个")
                return False
        
        print("✅ all_articles 格式验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_load_failed_urls():
    """测试加载失败URL文件"""
    print("=" * 60)
    print("测试加载失败URL文件")
    print("=" * 60)
    
    try:
        from core.failed_url_processor import FailedUrlProcessor
        
        processor = FailedUrlProcessor()
        
        # 创建临时测试文件
        test_urls = [
            'https://example.com/test1',
            'https://example.com/test2',
            'https://example.com/test3'
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            temp_file = f.name
            create_test_failed_csv(temp_file, test_urls)
        
        try:
            # 测试加载
            failed_urls = processor.load_failed_urls(temp_file)
            
            print(f"✅ 加载成功，共 {len(failed_urls)} 个URL")
            for i, url_info in enumerate(failed_urls):
                print(f"   {i+1}. {url_info['failed_url']} - {url_info['title']}")
            
            if len(failed_urls) == len(test_urls):
                print("✅ URL数量验证通过")
                return True
            else:
                print(f"❌ URL数量不匹配: 期望 {len(test_urls)}，实际 {len(failed_urls)}")
                return False
                
        finally:
            # 清理临时文件
            os.unlink(temp_file)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_routing_architecture():
    """测试路由架构（模拟测试，不实际爬取）"""
    print("=" * 60)
    print("测试路由架构")
    print("=" * 60)
    
    try:
        from core.failed_url_processor import process_failed_csv_via_crawler
        
        # 创建临时测试文件
        test_urls = [
            'https://httpbin.org/html',  # 使用测试URL
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            temp_file = f.name
            create_test_failed_csv(temp_file, test_urls)
        
        try:
            print(f"📁 测试文件: {temp_file}")
            
            # 测试日志回调
            def log_callback(message):
                print(f"📝 {message}")
            
            # 测试进度回调
            def progress_callback(current, total):
                print(f"📊 进度: {current}/{total}")
            
            # 使用新的路由方式
            result = await process_failed_csv_via_crawler(
                failed_csv_path=temp_file,
                save_dir="test_output",
                export_filename="test_routing",
                file_format="CSV",
                classid="test_class",
                retry=1,
                interval=0.1,
                max_workers=1,
                progress_callback=progress_callback,
                log_callback=log_callback,
                mode="balance",
                content_selectors=['.content', 'body']  # 简单的选择器用于测试
            )
            
            print(f"✅ 路由测试完成")
            print(f"   结果: {result}")
            
            # 验证结果格式
            expected_keys = ['total', 'success', 'failed']
            for key in expected_keys:
                if key not in result:
                    print(f"❌ 结果缺少字段: {key}")
                    return False
            
            print("✅ 路由架构测试通过")
            return True
            
        finally:
            # 清理临时文件
            os.unlink(temp_file)
            # 清理测试输出目录
            import shutil
            if os.path.exists("test_output"):
                shutil.rmtree("test_output")
        
    except Exception as e:
        print(f"❌ 路由架构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_import_functions():
    """测试导入函数"""
    print("=" * 60)
    print("测试导入函数")
    print("=" * 60)
    
    try:
        from core.failed_url_processor import _import_crawler_functions
        
        save_article_async, crawl_articles_async = _import_crawler_functions()
        
        if save_article_async is None or crawl_articles_async is None:
            print("❌ 导入爬虫函数失败")
            return False
        
        print("✅ 爬虫函数导入成功")
        print(f"   save_article_async: {save_article_async.__name__}")
        print(f"   crawl_articles_async: {crawl_articles_async.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试 failed_url_processor.py 新路由架构")
    print()
    
    tests = [
        ("导入函数测试", test_import_functions),
        ("convert_to_all_articles 方法测试", test_convert_to_all_articles),
        ("加载失败URL文件测试", test_load_failed_urls),
        ("路由架构测试", test_routing_architecture),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新路由架构工作正常。")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    asyncio.run(main())
