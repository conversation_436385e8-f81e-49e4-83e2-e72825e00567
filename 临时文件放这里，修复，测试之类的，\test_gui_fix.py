#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI修复后的功能
"""

import sys
import os

def test_gui_creation():
    """测试GUI创建"""
    try:
        from PyQt5.QtWidgets import QApplication
        from crawler_gui_new import CrawlerGUI
        
        print("测试GUI创建...")
        
        # 创建应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = CrawlerGUI()
        print("✅ GUI创建成功")
        
        # 检查模组配置相关控件
        checks = [
            ('module_enabled', '模组开关'),
            ('module_list', '模组列表'),
            ('default_module', '默认模组'),
            ('failed_file_path', '失败文件路径'),
            ('save_dir_path', '保存目录路径'),
            ('retry_filename', '重试文件名'),
            ('retry_count', '重试次数'),
            ('retry_interval', '重试间隔'),
            ('retry_workers', '并发数'),
            ('process_failed_btn', '处理按钮')
        ]
        
        for attr, name in checks:
            if hasattr(gui, attr):
                print(f"✅ {name}控件存在")
            else:
                print(f"❌ {name}控件缺失")
        
        # 检查标签页
        tab_count = gui.tab_widget.count()
        tab_names = [gui.tab_widget.tabText(i) for i in range(tab_count)]
        print(f"✅ 标签页: {tab_names}")
        
        # 测试配置获取
        config = gui.get_config_from_gui()
        if 'module_config' in config:
            print("✅ 配置中包含模组配置")
        else:
            print("❌ 配置中缺少模组配置")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_module_functionality():
    """测试模组功能"""
    try:
        from module_manager import module_manager, match_module_for_url
        
        print("\n测试模组功能...")
        
        # 测试模组列表
        modules = module_manager.list_modules()
        print(f"✅ 找到 {len(modules)} 个模组: {modules}")
        
        # 测试URL匹配
        test_urls = [
            "https://mp.weixin.qq.com/s/test",
            "https://www.shrd.gov.cn/test",
            "https://www.example.com/test"
        ]
        
        for url in test_urls:
            module_name = match_module_for_url(url)
            print(f"✅ {url} -> {module_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模组功能测试失败: {e}")
        return False

def test_failed_url_processor():
    """测试失败URL处理器"""
    try:
        from failed_url_processor import FailedUrlProcessor
        
        print("\n测试失败URL处理器...")
        
        processor = FailedUrlProcessor()
        print("✅ 失败URL处理器创建成功")
        
        # 检查失败文件
        articles_dir = "articles"
        if os.path.exists(articles_dir):
            failed_files = [f for f in os.listdir(articles_dir) if f.endswith("_failed.csv")]
            print(f"✅ 找到 {len(failed_files)} 个失败文件")
        else:
            print("ℹ️ articles目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 失败URL处理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("GUI修复验证测试")
    print("="*50)
    
    tests = [
        ("模组功能测试", test_module_functionality),
        ("失败URL处理器测试", test_failed_url_processor),
        ("GUI创建测试", test_gui_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "="*50)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！GUI修复成功。")
        print("\n现在您可以:")
        print("1. 运行 python crawler_gui_new.py 启动GUI")
        print("2. 在'模组配置'标签页中管理模组")
        print("3. 处理失败URL文件并自定义保存目录")
        print("4. 享受完整的模组配置功能")
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")

if __name__ == "__main__":
    main()
