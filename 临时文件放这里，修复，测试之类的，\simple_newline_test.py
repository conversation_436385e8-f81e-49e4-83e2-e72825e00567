#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 简单的换行符保留测试
try:
    from core.txt_clear import enhanced_content_filter
    
    # 测试内容
    test_text = """<div>
    <h1>标题</h1>
    
    <p>第一段内容。</p>
    
    <p>第二段内容。</p>
    
    <div class="meta">发布时间：2025-01-11</div>
    
    <p>第三段内容。</p>
    </div>"""
    
    print("原始内容:")
    print(repr(test_text))
    print("\n显示效果:")
    print(test_text)
    
    # 处理内容
    result = enhanced_content_filter(test_text)
    
    print("\n处理后内容:")
    print(repr(result))
    print("\n显示效果:")
    print(result)
    
    print(f"\n换行符数量: {result.count('\\n')}")
    print("测试完成!")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
