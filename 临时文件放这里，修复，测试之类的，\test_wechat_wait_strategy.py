#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信公众号等待策略修复
"""

import asyncio
from playwright.async_api import async_playwright
import time

async def test_wait_strategies():
    """测试不同的等待策略"""
    url = "https://mp.weixin.qq.com/s/hZmZVjxiQGmpS7z3Tjtg5g"
    
    strategies = [
        {
            "name": "domcontentloaded + 8秒等待",
            "wait_until": "domcontentloaded",
            "timeout": 60000,
            "extra_wait": 8
        },
        {
            "name": "load + 5秒等待", 
            "wait_until": "load",
            "timeout": 60000,
            "extra_wait": 5
        },
        {
            "name": "networkidle + 2秒等待",
            "wait_until": "networkidle",
            "timeout": 90000,
            "extra_wait": 2
        }
    ]
    
    for strategy in strategies:
        print(f"\n🧪 测试策略: {strategy['name']}")
        print("-" * 50)
        
        start_time = time.time()
        success = False
        content_length = 0
        
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=False)
                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )
                
                # 反检测
                await context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                """)
                
                page = await context.new_page()
                
                try:
                    # 使用当前策略访问页面
                    print(f"📱 访问页面，等待策略: {strategy['wait_until']}")
                    await page.goto(url, 
                                  timeout=strategy['timeout'], 
                                  wait_until=strategy['wait_until'])
                    
                    print(f"⏳ 额外等待 {strategy['extra_wait']} 秒...")
                    await asyncio.sleep(strategy['extra_wait'])
                    
                    # 测试内容提取
                    content_selectors = [
                        "#js_content",
                        ".rich_media_content",
                        "#img-content"
                    ]
                    
                    for selector in content_selectors:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                text = await element.inner_text()
                                if text and len(text.strip()) > 100:
                                    content_length = len(text.strip())
                                    success = True
                                    print(f"✅ 找到内容: {selector}, 长度: {content_length}")
                                    break
                        except:
                            continue
                    
                    if not success:
                        print("❌ 未找到有效内容")
                    
                except Exception as e:
                    print(f"❌ 访问失败: {e}")
                
                finally:
                    await browser.close()
                    
        except Exception as e:
            print(f"❌ 策略失败: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"📊 结果:")
        print(f"  成功: {'是' if success else '否'}")
        print(f"  内容长度: {content_length}")
        print(f"  耗时: {duration:.1f}秒")
        
        # 间隔等待
        if strategy != strategies[-1]:
            print("⏳ 等待5秒后测试下一个策略...")
            await asyncio.sleep(5)

async def test_current_crawler_method():
    """测试当前爬虫方法"""
    print("\n🔧 测试当前爬虫方法")
    print("=" * 50)
    
    try:
        from crawler import save_article
        
        url = "https://mp.weixin.qq.com/s/hZmZVjxiQGmpS7z3Tjtg5g"
        
        print(f"📋 测试URL: {url}")
        print("🚀 开始爬取...")
        
        start_time = time.time()
        
        result = save_article(
            link=url,
            save_dir="test_articles",
            page_title="测试微信文章",
            content_selectors=["#js_content", ".rich_media_content"],
            title_selectors=["#activity-name", ".rich_media_title"],
            date_selectors=["#publish_time", ".rich_media_meta_text"],
            source_selectors=[".rich_media_meta_nickname", "#js_name"],
            content_type='CSS',
            title_selector_type='CSS',
            date_selector_type='CSS',
            source_selector_type='CSS',
            collect_links=True,
            mode='safe',
            export_filename="test_wechat",
            classid="test",
            file_format="CSV",
            retry=3,
            interval=1
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"📊 爬取结果:")
        print(f"  成功: {'是' if result else '否'}")
        print(f"  耗时: {duration:.1f}秒")
        
        if result:
            print("✅ 爬取成功！检查test_articles目录")
        else:
            print("❌ 爬取失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("微信公众号等待策略测试")
    print("=" * 60)
    
    # 测试不同等待策略
    await test_wait_strategies()
    
    # 测试当前爬虫方法
    await test_current_crawler_method()
    
    print("\n🎉 测试完成!")
    print("\n💡 建议:")
    print("1. 如果domcontentloaded策略成功率最高，说明修复有效")
    print("2. 如果仍然失败，可能需要进一步优化反检测")
    print("3. 观察哪种策略的内容提取最完整")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
