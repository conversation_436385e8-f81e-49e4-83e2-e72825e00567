#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数同步验证测试脚本
验证所有修改后的代码参数是否正确同步
"""

import sys
import os
import inspect
import json
from pathlib import Path

def test_crawler_function_signatures():
    """测试爬虫函数签名是否正确"""
    print("🔍 测试爬虫函数签名...")
    
    try:
        from core import crawler
        
        # 测试主要函数的参数
        functions_to_test = [
            'crawl_articles_async',
            'save_article',
            'save_article_async',
            'process_articles_batch'
        ]
        
        for func_name in functions_to_test:
            if hasattr(crawler, func_name):
                func = getattr(crawler, func_name)
                sig = inspect.signature(func)
                params = list(sig.parameters.keys())
                
                print(f"  ✅ {func_name}: {len(params)} 参数")
                
                # 检查是否包含新的复数形式参数
                expected_params = ['title_selectors', 'date_selectors', 'source_selectors']
                for param in expected_params:
                    if param in params:
                        print(f"    ✅ 包含 {param}")
                    else:
                        print(f"    ⚠️  缺少 {param}")
                
                # 检查是否还有旧的单数形式参数
                old_params = ['title_selector', 'date_selector', 'source_selector']
                for param in old_params:
                    if param in params:
                        print(f"    ⚠️  仍包含旧参数 {param}")
                
                # 检查是否还有类型参数
                type_params = ['title_selectors_type', 'date_selectors_type', 'source_selectors_type']
                for param in type_params:
                    if param in params:
                        print(f"    ⚠️  仍包含类型参数 {param}")
            else:
                print(f"  ❌ 函数 {func_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_config_files():
    """测试配置文件格式是否正确"""
    print("\n🔍 测试配置文件格式...")
    
    config_files = [
        'configs/crawler/crawler_config.json',
        'configs/modules/module_configs.json',
        'configs/app/config.json'
    ]
    
    success = True
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"  ✅ {config_file} 格式正确")
                
                # 检查是否使用了复数形式的选择器
                def check_selectors(obj, path=""):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            current_path = f"{path}.{key}" if path else key
                            
                            # 检查复数形式选择器
                            if key in ['title_selectors', 'date_selectors', 'source_selectors']:
                                print(f"    ✅ 发现复数选择器: {current_path}")
                            
                            # 检查是否还有单数形式选择器
                            if key in ['title_selector', 'date_selector', 'source_selector']:
                                print(f"    ⚠️  发现单数选择器: {current_path}")
                            
                            # 检查是否还有类型参数
                            if key.endswith('_selector_type') and key != 'content_type':
                                print(f"    ⚠️  发现类型参数: {current_path}")
                            
                            check_selectors(value, current_path)
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            check_selectors(item, f"{path}[{i}]")
                
                check_selectors(config)
                
            except Exception as e:
                print(f"  ❌ {config_file} 解析失败: {e}")
                success = False
        else:
            print(f"  ⚠️  {config_file} 不存在")
    
    return success

def test_gui_imports():
    """测试GUI模块导入是否正确"""
    print("\n🔍 测试GUI模块导入...")
    
    try:
        from gui import main_window, crawler_thread, config_manager
        print("  ✅ GUI模块导入成功")
        
        # 测试主要类是否存在
        if hasattr(main_window, 'CrawlerGUI'):
            print("  ✅ CrawlerGUI 类存在")
        else:
            print("  ❌ CrawlerGUI 类不存在")
        
        if hasattr(crawler_thread, 'CrawlerThread'):
            print("  ✅ CrawlerThread 类存在")
        else:
            print("  ❌ CrawlerThread 类不存在")
        
        return True
        
    except Exception as e:
        print(f"  ❌ GUI模块导入失败: {e}")
        return False

def test_module_system():
    """测试模组系统是否正常"""
    print("\n🔍 测试模组系统...")
    
    try:
        from modules import manager, config_manager
        print("  ✅ 模组系统导入成功")
        
        # 测试模组管理器
        if hasattr(manager, 'module_manager'):
            print("  ✅ module_manager 实例存在")
        else:
            print("  ❌ module_manager 实例不存在")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模组系统导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 参数同步验证测试")
    print("=" * 60)
    
    tests = [
        ("爬虫函数签名测试", test_crawler_function_signatures),
        ("配置文件格式测试", test_config_files),
        ("GUI模块导入测试", test_gui_imports),
        ("模组系统测试", test_module_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("\n🎉 所有参数同步验证测试通过！")
        print("✅ 参数统一化完成")
        print("✅ 配置文件格式正确")
        print("✅ 模块导入正常")
        print("✅ 系统功能完整")
    else:
        print("\n⚠️  部分测试失败，请检查相关问题")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
