#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL去重功能演示脚本
展示crawl_articles_async函数中URL去重的实际应用
"""

import asyncio
import sys
import os

async def demo_url_deduplication_in_crawling():
    """演示在爬取过程中的URL去重功能"""
    print("=" * 80)
    print("🚀 演示crawl_articles_async函数中的URL去重功能")
    print("=" * 80)
    
    try:
        from core.crawler import crawl_articles_async
        print("✅ 爬取函数导入成功")
        
        # 模拟预处理的文章列表（包含重复URL）
        mock_articles = [
            {
                "title": "重要新闻1",
                "url": "https://example.com/news/important-1",
                "date": "2025-01-01",
                "source": "新闻网"
            },
            {
                "title": "重要新闻2", 
                "url": "https://example.com/news/important-2",
                "date": "2025-01-02",
                "source": "新闻网"
            },
            {
                "title": "重要新闻1（重复）",  # 重复的URL
                "url": "https://example.com/news/important-1",
                "date": "2025-01-01",
                "source": "新闻网"
            },
            {
                "title": "重要新闻3",
                "url": "https://example.com/news/important-3?utm_source=homepage",
                "date": "2025-01-03",
                "source": "新闻网"
            },
            {
                "title": "重要新闻3（带不同参数）",  # 规范化后重复
                "url": "https://example.com/news/important-3?from=mobile&utm_campaign=app",
                "date": "2025-01-03",
                "source": "新闻网"
            },
            {
                "title": "重要新闻4",
                "url": "https://example.com/news/important-4#section1",
                "date": "2025-01-04",
                "source": "新闻网"
            },
            {
                "title": "重要新闻4（不同锚点）",  # 规范化后重复
                "url": "https://example.com/news/important-4#section2",
                "date": "2025-01-04",
                "source": "新闻网"
            },
            {
                "title": "重要新闻5",
                "url": "https://example.com/news/important-5",
                "date": "2025-01-05",
                "source": "新闻网"
            }
        ]
        
        print(f"📊 模拟文章数据: {len(mock_articles)} 篇文章")
        print("📝 文章列表:")
        for i, article in enumerate(mock_articles, 1):
            print(f"  {i}. {article['title']}")
            print(f"     URL: {article['url']}")
        
        print(f"\n🔄 调用crawl_articles_async进行处理...")
        print("   (使用all_articles参数，跳过实际爬取，直接进行去重和处理)")
        
        # 定义日志回调函数
        def log_callback(message):
            print(f"📋 {message}")
        
        # 调用crawl_articles_async，传入预处理的文章列表
        # 这将触发URL去重处理
        result = await crawl_articles_async(
            all_articles=mock_articles,
            log_callback=log_callback,
            export_filename="demo_deduplication_result",
            file_format="CSV",
            content_selectors=[".content", ".article-content"],  # 模拟选择器
            title_selectors=[".title", "h1"],
            date_selectors=[".date", ".publish-time"],
            source_selectors=[".source", ".author"]
        )
        
        print(f"\n📊 处理结果:")
        print(f"   总计处理: {result.get('total', 0)} 篇文章")
        print(f"   成功处理: {result.get('success', 0)} 篇文章")
        print(f"   失败处理: {result.get('failed', 0)} 篇文章")
        
        if result.get('success', 0) == 5:  # 预期去重后应该有5篇文章
            print("✅ URL去重功能在实际爬取中工作正常！")
            return True
        else:
            print(f"❌ URL去重结果异常: 预期5篇成功，实际{result.get('success', 0)}篇")
            return False
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_url_normalization_examples():
    """演示URL规范化的实际例子"""
    print("\n" + "=" * 80)
    print("🔧 URL规范化实际应用示例")
    print("=" * 80)
    
    try:
        from core.crawler import normalize_url_for_deduplication
        print("✅ URL规范化函数导入成功")
        
        # 实际网站的URL示例
        real_world_examples = [
            {
                "original": "https://mp.weixin.qq.com/s/abc123?from=timeline&isappinstalled=0",
                "description": "微信公众号文章（移除跟踪参数）"
            },
            {
                "original": "https://www.zhzx.gov.cn/news/detail.html?id=123&utm_source=homepage",
                "description": "政府网站新闻（移除UTM参数）"
            },
            {
                "original": "https://news.example.com/article/2025/01/news.html#comments",
                "description": "新闻网站（移除锚点）"
            },
            {
                "original": "https://www.bjrd.gov.cn/article.jsp?id=456&sessionid=xyz&timestamp=1234567890",
                "description": "人大网站（移除会话和时间戳参数）"
            },
            {
                "original": "https://example.com/page?param1=value1&utm_campaign=test&param2=value2#section1",
                "description": "复杂URL（保留有用参数，移除跟踪参数和锚点）"
            }
        ]
        
        print("🌐 实际网站URL规范化示例:")
        for i, example in enumerate(real_world_examples, 1):
            normalized = normalize_url_for_deduplication(example["original"])
            print(f"\n{i}. {example['description']}")
            print(f"   原始URL: {example['original']}")
            print(f"   规范化: {normalized}")
            
            # 检查是否确实进行了规范化
            if normalized != example["original"]:
                print(f"   ✅ 成功规范化")
            else:
                print(f"   ℹ️ 无需规范化")
        
        return True
        
    except Exception as e:
        print(f"❌ URL规范化演示失败: {e}")
        return False


async def main():
    """主演示函数"""
    print("🎯 URL去重功能完整演示")
    
    results = []
    
    # 演示在爬取中的URL去重
    results.append(await demo_url_deduplication_in_crawling())
    
    # 演示URL规范化
    results.append(demo_url_normalization_examples())
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 演示结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 成功演示: {passed}/{total}")
    
    if passed == total:
        print("🎉 URL去重功能演示完成！")
        print("\n💡 功能特点:")
        print("   • 自动识别重复URL并去除")
        print("   • 智能规范化URL（移除跟踪参数、锚点等）")
        print("   • 支持多种URL字段名（url、href、link）")
        print("   • 处理边界情况（空URL、不同数据结构）")
        print("   • 详细的去重统计和日志")
        print("\n🚀 使用方式:")
        print("   在crawl_articles_async函数中，URL去重会自动执行")
        print("   无需额外配置，首要任务就是去重处理")
        return True
    else:
        print("❌ 部分演示失败")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
