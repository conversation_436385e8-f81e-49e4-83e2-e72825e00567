#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI集成的选择器编辑功能
验证选择器编辑功能是否正确集成到主界面
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gui_structure():
    """测试GUI结构"""
    print("=" * 60)
    print("测试GUI结构")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查字段配置标签页结构
        has_fields_tab = hasattr(window, 'fields_tab')
        has_sub_tab_widget = hasattr(window, 'fields_sub_tab_widget')
        has_field_selection_tab = hasattr(window, 'field_selection_tab')
        has_field_selectors_tab = hasattr(window, 'field_selectors_tab')
        
        print(f"📋 字段配置标签页: {'✅' if has_fields_tab else '❌'}")
        print(f"📋 子标签页控件: {'✅' if has_sub_tab_widget else '❌'}")
        print(f"📋 字段选择子标签页: {'✅' if has_field_selection_tab else '❌'}")
        print(f"📋 选择器编辑子标签页: {'✅' if has_field_selectors_tab else '❌'}")
        
        # 检查子标签页数量
        if has_sub_tab_widget:
            tab_count = window.fields_sub_tab_widget.count()
            print(f"📊 子标签页数量: {tab_count}")
            
            for i in range(tab_count):
                tab_text = window.fields_sub_tab_widget.tabText(i)
                print(f"   标签页 {i+1}: {tab_text}")
        
        return has_fields_tab and has_sub_tab_widget and has_field_selection_tab and has_field_selectors_tab
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_editor_components():
    """测试选择器编辑器组件"""
    print("=" * 60)
    print("测试选择器编辑器组件")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查选择器编辑相关组件
        components = [
            ('enable_selector_edit_checkbox', '选择器编辑开关'),
            ('selector_edit_status_label', '选择器编辑状态指示器'),
            ('selector_editor_group', '选择器编辑器组'),
            ('selector_actions_group', '选择器操作组'),
            ('field_selector_editors', '字段选择器编辑器字典'),
            ('selector_editors_layout', '选择器编辑器布局')
        ]
        
        component_count = 0
        for attr_name, display_name in components:
            has_component = hasattr(window, attr_name)
            print(f"🔧 {display_name}: {'✅' if has_component else '❌'}")
            if has_component:
                component_count += 1
        
        print(f"📊 组件完整性: {component_count}/{len(components)}")
        
        # 检查选择器编辑器字典
        if hasattr(window, 'field_selector_editors'):
            editor_count = len(window.field_selector_editors)
            print(f"📝 选择器编辑器数量: {editor_count}")
            
            if editor_count > 0:
                # 显示前几个编辑器
                field_names = list(window.field_selector_editors.keys())[:5]
                print(f"📋 示例字段: {field_names}")
        
        return component_count >= len(components) * 0.8  # 至少80%的组件存在
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_switch_functionality():
    """测试选择器开关功能"""
    print("=" * 60)
    print("测试选择器开关功能")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        if not hasattr(window, 'enable_selector_edit_checkbox'):
            print("❌ 选择器编辑开关不存在")
            return False
        
        # 测试启用功能
        print("\n🔧 测试启用选择器编辑...")
        window.enable_selector_edit_checkbox.setChecked(True)
        
        # 检查相关组件状态
        controlled_components = [
            ('selector_editor_group', '选择器编辑器组'),
            ('selector_actions_group', '选择器操作组')
        ]
        
        enabled_count = 0
        for attr_name, display_name in controlled_components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                is_enabled = component.isEnabled()
                print(f"   {display_name}: {'✅ 启用' if is_enabled else '❌ 禁用'}")
                if is_enabled:
                    enabled_count += 1
            else:
                print(f"   {display_name}: ⚠️ 不存在")
        
        # 测试禁用功能
        print("\n🔧 测试禁用选择器编辑...")
        window.enable_selector_edit_checkbox.setChecked(False)
        
        disabled_count = 0
        for attr_name, display_name in controlled_components:
            if hasattr(window, attr_name):
                component = getattr(window, attr_name)
                is_enabled = component.isEnabled()
                print(f"   {display_name}: {'⚠️ 仍启用' if is_enabled else '✅ 已禁用'}")
                if not is_enabled:
                    disabled_count += 1
            else:
                print(f"   {display_name}: ⚠️ 不存在")
        
        # 检查状态指示器
        if hasattr(window, 'selector_edit_status_label'):
            status_text = window.selector_edit_status_label.text()
            print(f"📊 状态指示器: {status_text}")
        
        return enabled_count > 0 and disabled_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_editor_creation():
    """测试选择器编辑器创建"""
    print("=" * 60)
    print("测试选择器编辑器创建")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查选择器编辑器是否正确创建
        if not hasattr(window, 'field_selector_editors'):
            print("❌ 选择器编辑器字典不存在")
            return False
        
        editors = window.field_selector_editors
        editor_count = len(editors)
        print(f"📝 选择器编辑器数量: {editor_count}")
        
        if editor_count == 0:
            print("⚠️ 没有创建任何选择器编辑器")
            return False
        
        # 检查编辑器类型和功能
        valid_editors = 0
        for field_name, editor in editors.items():
            try:
                # 检查编辑器是否是QLineEdit
                from PyQt5.QtWidgets import QLineEdit
                if isinstance(editor, QLineEdit):
                    # 测试基本功能
                    original_text = editor.text()
                    test_text = ".test-selector"
                    editor.setText(test_text)
                    
                    if editor.text() == test_text:
                        valid_editors += 1
                        print(f"   ✅ {field_name}: 编辑器功能正常")
                    else:
                        print(f"   ❌ {field_name}: 编辑器功能异常")
                    
                    # 恢复原始文本
                    editor.setText(original_text)
                else:
                    print(f"   ❌ {field_name}: 编辑器类型错误")
                    
            except Exception as e:
                print(f"   ❌ {field_name}: 编辑器测试失败 - {e}")
        
        print(f"📊 有效编辑器: {valid_editors}/{editor_count}")
        
        return valid_editors > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_operations():
    """测试选择器操作"""
    print("=" * 60)
    print("测试选择器操作")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查选择器操作方法
        operation_methods = [
            ('reset_selectors_to_default', '重置选择器为默认'),
            ('test_current_selectors', '测试当前选择器'),
            ('save_current_selectors', '保存当前选择器'),
            ('test_single_field_selector', '测试单个字段选择器'),
            ('load_selector_editors', '加载选择器编辑器'),
            ('on_selector_edit_enabled_changed', '选择器编辑开关事件处理')
        ]
        
        method_count = 0
        for method_name, display_name in operation_methods:
            has_method = hasattr(window, method_name) and callable(getattr(window, method_name))
            print(f"🔧 {display_name}: {'✅' if has_method else '❌'}")
            if has_method:
                method_count += 1
        
        print(f"📊 操作方法完整性: {method_count}/{len(operation_methods)}")
        
        # 测试加载选择器编辑器方法
        if hasattr(window, 'load_selector_editors'):
            try:
                print("\n🧪 测试加载选择器编辑器...")
                window.load_selector_editors()
                print("   ✅ 加载选择器编辑器成功")
            except Exception as e:
                print(f"   ❌ 加载选择器编辑器失败: {e}")
        
        return method_count >= len(operation_methods) * 0.8  # 至少80%的方法存在
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试GUI集成的选择器编辑功能")
    print()
    
    tests = [
        ("GUI结构测试", test_gui_structure),
        ("选择器编辑器组件测试", test_selector_editor_components),
        ("选择器开关功能测试", test_selector_switch_functionality),
        ("选择器编辑器创建测试", test_selector_editor_creation),
        ("选择器操作测试", test_selector_operations),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI集成的选择器编辑功能正常。")
        print("\n💡 使用说明:")
        print("   1. 在字段配置标签页中找到'选择器编辑'子标签页")
        print("   2. 勾选'启用选择器编辑'复选框")
        print("   3. 在编辑器中为每个字段配置CSS选择器")
        print("   4. 使用'测试'按钮验证单个字段选择器")
        print("   5. 使用'保存选择器'按钮保存配置")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
