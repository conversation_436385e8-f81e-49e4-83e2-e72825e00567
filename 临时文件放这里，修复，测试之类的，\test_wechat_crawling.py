#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信公众号爬取问题
"""

import asyncio
from playwright.async_api import async_playwright
import time

async def test_wechat_url(url):
    """测试单个微信公众号URL"""
    print(f"\n🔍 测试URL: {url}")
    
    async with async_playwright() as p:
        # 启动浏览器 - 使用更真实的配置
        browser = await p.chromium.launch(
            headless=False,  # 先用非无头模式观察
            args=[
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation",
                "--disable-extensions",
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        # 添加反检测脚本
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            window.chrome = { runtime: {} };
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
        """)
        
        page = await context.new_page()
        
        try:
            print("📱 正在访问页面...")
            
            # 访问页面
            response = await page.goto(url, timeout=60000, wait_until="networkidle")
            print(f"📊 响应状态: {response.status}")
            
            # 等待页面加载
            await page.wait_for_load_state('networkidle', timeout=30000)
            await asyncio.sleep(3)  # 额外等待
            
            # 获取页面标题
            title = await page.title()
            print(f"📝 页面标题: {title}")
            
            # 检查是否被重定向或阻止
            current_url = page.url
            print(f"🔗 当前URL: {current_url}")
            
            if "mp.weixin.qq.com" not in current_url:
                print("⚠️ 页面被重定向，可能被阻止访问")
                return False
            
            # 测试不同的选择器
            selectors_to_test = [
                "#js_content",
                ".rich_media_content",
                "#img-content", 
                ".rich_media_area_primary",
                "[data-role='outer']",
                ".rich_media_wrp"
            ]
            
            print("🔍 测试内容选择器:")
            content_found = False
            
            for selector in selectors_to_test:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        text = await element.inner_text()
                        text_length = len(text.strip())
                        print(f"  ✅ {selector}: 找到内容，长度 {text_length}")
                        if text_length > 100:
                            content_found = True
                            print(f"     内容预览: {text[:100]}...")
                    else:
                        print(f"  ❌ {selector}: 未找到")
                except Exception as e:
                    print(f"  ❌ {selector}: 错误 - {e}")
            
            # 测试标题选择器
            print("\n🔍 测试标题选择器:")
            title_selectors = [
                "#activity-name",
                ".rich_media_title",
                "h1.rich_media_title",
                "h1",
                ".title"
            ]
            
            for selector in title_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        text = await element.inner_text()
                        print(f"  ✅ {selector}: {text[:50]}...")
                    else:
                        print(f"  ❌ {selector}: 未找到")
                except Exception as e:
                    print(f"  ❌ {selector}: 错误 - {e}")
            
            # 检查页面是否需要验证
            verification_indicators = [
                "验证",
                "captcha", 
                "robot",
                "机器人",
                "请稍后再试",
                "访问过于频繁"
            ]
            
            page_content = await page.content()
            for indicator in verification_indicators:
                if indicator in page_content:
                    print(f"⚠️ 检测到可能的验证提示: {indicator}")
            
            # 截图保存
            screenshot_path = f"wechat_test_{int(time.time())}.png"
            await page.screenshot(path=screenshot_path)
            print(f"📸 页面截图已保存: {screenshot_path}")
            
            return content_found
            
        except Exception as e:
            print(f"❌ 访问失败: {e}")
            return False
        finally:
            await browser.close()

async def test_multiple_urls():
    """测试多个URL"""
    # 从失败文件中取几个URL测试
    test_urls = [
        "https://mp.weixin.qq.com/s/hZmZVjxiQGmpS7z3Tjtg5g",
        "https://mp.weixin.qq.com/s/S-WaozfqWdLLXimt6xi7xw",
        "https://mp.weixin.qq.com/s/-82vb0Rxxfn7iDqx_syoYQ"
    ]
    
    print("🚀 开始测试微信公众号URL访问")
    print("=" * 60)
    
    success_count = 0
    for i, url in enumerate(test_urls):
        print(f"\n📋 测试 {i+1}/{len(test_urls)}")
        success = await test_wechat_url(url)
        if success:
            success_count += 1
        
        # 间隔等待，避免被检测
        if i < len(test_urls) - 1:
            print("⏳ 等待5秒后继续...")
            await asyncio.sleep(5)
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{len(test_urls)} 成功")
    
    if success_count == 0:
        print("\n💡 建议:")
        print("1. 微信公众号可能需要登录才能访问完整内容")
        print("2. 可能需要更复杂的反检测策略")
        print("3. 考虑使用代理或更换访问策略")
        print("4. 某些文章可能已被删除或限制访问")

def main():
    """主函数"""
    print("微信公众号爬取问题诊断")
    print("=" * 40)
    
    try:
        asyncio.run(test_multiple_urls())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    main()
