#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel写入优化测试脚本
测试不同写入策略的性能和正确性
"""

import os
import sys
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加core目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

try:
    from crawler import (
        safe_excel_write,
        smart_excel_write,
        batch_excel_write,
        hybrid_write_strategy,
        flush_excel_cache,
        finalize_hybrid_files,
        EXCEL_AVAILABLE
    )
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def test_performance():
    """性能测试"""
    if not EXCEL_AVAILABLE:
        print("❌ Excel不可用，跳过测试")
        return
    
    print("🚀 开始Excel写入性能测试...")
    
    # 测试数据
    headers = ['日期', '来源', '标题', '链接', '内容', '分类', '城市', '获取时间']
    test_data = []
    
    for i in range(1000):
        test_data.append([
            f"2024-01-{i%30+1:02d}",
            f"测试来源{i%10}",
            f"测试标题{i}",
            f"http://test.com/article{i}",
            f"这是测试内容{i}" * 10,  # 模拟较长内容
            f"分类{i%5}",
            f"城市{i%20}",
            "2024-01-01 12:00:00"
        ])
    
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    # 测试1: 传统单行写入
    print("\n📝 测试1: 传统单行写入")
    file1 = os.path.join(test_dir, "test_traditional.xlsx")
    if os.path.exists(file1):
        os.remove(file1)
    
    start_time = time.time()
    for i, row in enumerate(test_data[:100]):  # 只测试100行，避免太慢
        safe_excel_write(file1, row, headers if i == 0 else None, use_batch=False)
    traditional_time = time.time() - start_time
    print(f"   传统写入100行耗时: {traditional_time:.2f}秒")
    
    # 测试2: 批量写入
    print("\n📦 测试2: 批量写入")
    file2 = os.path.join(test_dir, "test_batch.xlsx")
    if os.path.exists(file2):
        os.remove(file2)
    
    start_time = time.time()
    batch_excel_write(file2, test_data[:1000], headers)
    batch_time = time.time() - start_time
    print(f"   批量写入1000行耗时: {batch_time:.2f}秒")
    
    # 测试3: 智能批量写入
    print("\n🧠 测试3: 智能批量写入")
    file3 = os.path.join(test_dir, "test_smart.xlsx")
    if os.path.exists(file3):
        os.remove(file3)
    
    start_time = time.time()
    for i, row in enumerate(test_data[:1000]):
        smart_excel_write(file3, row, headers if i == 0 else None, batch_size=50)
    # 刷新缓存
    flush_excel_cache(file3)
    smart_time = time.time() - start_time
    print(f"   智能批量写入1000行耗时: {smart_time:.2f}秒")
    
    # 测试4: 混合策略
    print("\n🔄 测试4: 混合策略")
    file4 = os.path.join(test_dir, "test_hybrid.xlsx")
    if os.path.exists(file4):
        os.remove(file4)
    
    start_time = time.time()
    for i, row in enumerate(test_data[:1000]):
        hybrid_write_strategy(file4, row, headers if i == 0 else None, "EXCEL", threshold=100)
    hybrid_time = time.time() - start_time
    print(f"   混合策略写入1000行耗时: {hybrid_time:.2f}秒")
    
    # 性能对比
    print("\n📊 性能对比:")
    print(f"   传统写入(100行): {traditional_time:.2f}秒 (预估1000行: {traditional_time*10:.2f}秒)")
    print(f"   批量写入(1000行): {batch_time:.2f}秒")
    print(f"   智能批量(1000行): {smart_time:.2f}秒")
    print(f"   混合策略(1000行): {hybrid_time:.2f}秒")
    
    if batch_time > 0:
        print(f"\n🎯 性能提升:")
        print(f"   批量写入比传统写入快: {(traditional_time*10/batch_time):.1f}倍")
        print(f"   智能批量比传统写入快: {(traditional_time*10/smart_time):.1f}倍")
        print(f"   混合策略比传统写入快: {(traditional_time*10/hybrid_time):.1f}倍")

def test_correctness():
    """正确性测试"""
    if not EXCEL_AVAILABLE:
        print("❌ Excel不可用，跳过测试")
        return
    
    print("\n🔍 开始正确性测试...")
    
    headers = ['ID', '名称', '值']
    test_data = [
        [1, '测试1', '值1'],
        [2, '测试2', '值2'],
        [3, '测试3', '值3']
    ]
    
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    # 测试各种写入方式的正确性
    files_to_test = [
        ("test_correct_batch.xlsx", "批量写入"),
        ("test_correct_smart.xlsx", "智能批量"),
        ("test_correct_hybrid.xlsx", "混合策略")
    ]
    
    for filename, method in files_to_test:
        filepath = os.path.join(test_dir, filename)
        if os.path.exists(filepath):
            os.remove(filepath)
        
        if method == "批量写入":
            batch_excel_write(filepath, test_data, headers)
        elif method == "智能批量":
            for i, row in enumerate(test_data):
                smart_excel_write(filepath, row, headers if i == 0 else None, batch_size=2)
            flush_excel_cache(filepath)
        elif method == "混合策略":
            for i, row in enumerate(test_data):
                hybrid_write_strategy(filepath, row, headers if i == 0 else None, "EXCEL", threshold=2)
            # 完成最终转换
            finalize_hybrid_files(test_dir)
        
        # 验证文件内容
        try:
            import openpyxl
            wb = openpyxl.load_workbook(filepath)
            ws = wb.active
            
            # 检查行数
            expected_rows = len(test_data) + 1  # +1 for headers
            actual_rows = ws.max_row
            
            if actual_rows == expected_rows:
                print(f"   ✅ {method}: 行数正确 ({actual_rows}行)")
            else:
                print(f"   ❌ {method}: 行数错误 (期望{expected_rows}行，实际{actual_rows}行)")
            
            # 检查表头
            header_row = [cell.value for cell in ws[1]]
            if header_row == headers:
                print(f"   ✅ {method}: 表头正确")
            else:
                print(f"   ❌ {method}: 表头错误 (期望{headers}，实际{header_row})")
                
        except Exception as e:
            print(f"   ❌ {method}: 验证失败 - {e}")

def test_concurrent_write():
    """并发写入测试"""
    if not EXCEL_AVAILABLE:
        print("❌ Excel不可用，跳过测试")
        return
    
    print("\n🔄 开始并发写入测试...")
    
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    def write_worker(worker_id):
        """工作线程函数"""
        filepath = os.path.join(test_dir, f"test_concurrent_{worker_id}.xlsx")
        if os.path.exists(filepath):
            os.remove(filepath)
        
        headers = ['Worker', 'ID', '时间戳']
        
        for i in range(100):
            row = [f"Worker{worker_id}", i, time.time()]
            smart_excel_write(filepath, row, headers if i == 0 else None, batch_size=20)
        
        # 刷新缓存
        flush_excel_cache(filepath)
        return f"Worker{worker_id}完成"
    
    # 启动多个并发写入线程
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(write_worker, i) for i in range(3)]
        results = [future.result() for future in futures]
    
    concurrent_time = time.time() - start_time
    print(f"   3个并发写入完成，耗时: {concurrent_time:.2f}秒")
    
    for result in results:
        print(f"   ✅ {result}")

def main():
    """主函数"""
    print("=" * 60)
    print("📊 Excel写入优化测试")
    print("=" * 60)
    
    if not EXCEL_AVAILABLE:
        print("❌ openpyxl库未安装，无法进行Excel测试")
        print("请运行: pip install openpyxl")
        return
    
    try:
        # 运行所有测试
        test_performance()
        test_correctness()
        test_concurrent_write()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        print("📁 测试文件保存在 test_output 目录中")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
