#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL缓存功能使用演示
演示如何使用新的URL缓存功能避免重复翻页收集URL
"""

import asyncio
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.crawler import crawl_articles_async, check_url_cache_exists


async def demo_url_cache_workflow():
    """演示URL缓存的完整工作流程"""
    print("🚀 URL缓存功能演示")
    print("=" * 60)
    
    # 演示配置
    demo_config = {
        'input_url': "https://example.com/news/list.html",  # 替换为真实URL
        'config_group': "演示配置组",
        'max_pages': 3,
        'headless': True,
        'export_filename': "demo_cache_result",
        'file_format': "CSV",
        
        # 选择器配置（需要根据实际网站调整）
        'list_container_selector': ".main",
        'article_item_selector': "a",
        'content_selectors': [".content", ".article"],
        'title_selectors': ["title", "h1"],
    }
    
    print("📋 演示配置:")
    print(f"   列表页URL: {demo_config['input_url']}")
    print(f"   配置组: {demo_config['config_group']}")
    print(f"   最大页数: {demo_config['max_pages']}")
    print()
    
    # 第一步：检查是否存在缓存
    print("1️⃣ 检查URL缓存状态...")
    cache_exists, cache_path, cache_info = check_url_cache_exists(
        demo_config['input_url'], 
        demo_config['config_group']
    )
    
    if cache_exists:
        print(f"✅ 发现缓存文件: {cache_info['filename']}")
        print(f"   修改时间: {cache_info['modified_str']}")
        print(f"   文件大小: {cache_info['size']} bytes")
        print()
        
        # 演示使用缓存的爬取
        print("2️⃣ 使用缓存进行快速爬取...")
        print("💡 启用 skip_pagination_if_cached=True")
        
        try:
            result = await crawl_articles_async(
                input_url=demo_config['input_url'],
                config_group=demo_config['config_group'],
                skip_pagination_if_cached=True,  # 启用跳过翻页
                max_pages=demo_config['max_pages'],
                headless=demo_config['headless'],
                export_filename=demo_config['export_filename'] + "_cached",
                file_format=demo_config['file_format'],
                list_container_selector=demo_config['list_container_selector'],
                article_item_selector=demo_config['article_item_selector'],
                content_selectors=demo_config['content_selectors'],
                title_selectors=demo_config['title_selectors'],
                log_callback=print
            )
            
            print(f"\n✅ 缓存爬取完成:")
            print(f"   总计: {result.get('total', 0)} 篇")
            print(f"   成功: {result.get('success', 0)} 篇")
            print(f"   失败: {result.get('failed', 0)} 篇")
            
        except Exception as e:
            print(f"❌ 缓存爬取失败: {e}")
    
    else:
        print("📄 未发现缓存文件")
        print()
        
        # 演示第一次爬取（建立缓存）
        print("2️⃣ 第一次爬取（建立缓存）...")
        print("💡 设置 skip_pagination_if_cached=False")
        
        try:
            result = await crawl_articles_async(
                input_url=demo_config['input_url'],
                config_group=demo_config['config_group'],
                skip_pagination_if_cached=False,  # 第一次不跳过
                max_pages=demo_config['max_pages'],
                headless=demo_config['headless'],
                export_filename=demo_config['export_filename'] + "_first",
                file_format=demo_config['file_format'],
                list_container_selector=demo_config['list_container_selector'],
                article_item_selector=demo_config['article_item_selector'],
                content_selectors=demo_config['content_selectors'],
                title_selectors=demo_config['title_selectors'],
                log_callback=print
            )
            
            print(f"\n✅ 首次爬取完成:")
            print(f"   总计: {result.get('total', 0)} 篇")
            print(f"   成功: {result.get('success', 0)} 篇")
            print(f"   失败: {result.get('failed', 0)} 篇")
            
            # 检查是否成功创建了缓存
            cache_exists_after, _, cache_info_after = check_url_cache_exists(
                demo_config['input_url'], 
                demo_config['config_group']
            )
            
            if cache_exists_after:
                print(f"\n💾 缓存文件已创建: {cache_info_after['filename']}")
                print("   下次爬取可以使用 skip_pagination_if_cached=True 跳过翻页")
            
        except Exception as e:
            print(f"❌ 首次爬取失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 演示总结:")
    print("✅ URL缓存功能可以有效避免重复翻页收集URL")
    print("✅ 第一次爬取自动建立缓存，后续爬取可以跳过翻页")
    print("✅ 通过配置组可以管理不同网站的URL缓存")
    print("💡 在GUI中可以通过'跳过翻页（使用URL缓存）'选项控制")
    print("=" * 60)


def demo_gui_usage():
    """演示GUI中的使用方法"""
    print("\n🖥️ GUI使用说明:")
    print("=" * 40)
    print("1. 在'爬取设置'组中找到'跳过翻页（使用URL缓存）'选项")
    print("2. 在'配置组'字段中输入配置组名称（如：政府新闻）")
    print("3. 第一次爬取时不勾选'跳过翻页'选项")
    print("4. 第二次及以后爬取时勾选'跳过翻页'选项")
    print("5. 系统会自动检测缓存并跳过翻页收集步骤")
    print()
    print("📁 缓存文件位置: url_cache/[配置组]/")
    print("📝 文件命名格式: [域名]_[路径]_collected_urls.csv")
    print("=" * 40)


async def main():
    """主函数"""
    print("欢迎使用URL缓存功能演示！")
    print()
    
    # 显示GUI使用说明
    demo_gui_usage()
    
    # 询问是否运行实际演示
    try:
        response = input("\n是否运行实际爬取演示？(需要真实URL) (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            print("\n⚠️ 请注意：")
            print("1. 需要将demo_config中的URL替换为真实的网站URL")
            print("2. 需要根据实际网站调整选择器配置")
            print("3. 确保网站允许爬取并遵守robots.txt")
            print()
            
            confirm = input("确认继续？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                await demo_url_cache_workflow()
            else:
                print("演示取消")
        else:
            print("演示结束。请查看代码了解具体使用方法。")
    
    except KeyboardInterrupt:
        print("\n演示中断")


if __name__ == "__main__":
    asyncio.run(main())
