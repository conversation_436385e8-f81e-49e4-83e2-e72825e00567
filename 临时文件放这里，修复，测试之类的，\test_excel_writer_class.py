#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的ExcelWriter类功能
验证重构后的Excel功能是否正常工作
"""

import os
import sys
import time
import asyncio

# 添加core目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

def test_excel_writer_class():
    """测试ExcelWriter类的基本功能"""
    print("🧪 测试ExcelWriter类功能")
    print("=" * 60)
    
    try:
        from excel_writer import ExcelWriter, ExcelWriteMode, get_excel_writer
        print("✅ ExcelWriter类导入成功")
        
        # 测试数据
        headers = ['日期', '标题', 'URL', '内容']
        test_data = [
            ['2024-01-01', '测试标题1', 'http://test1.com', '测试内容1'],
            ['2024-01-02', '测试标题2', 'http://test2.com', '测试内容2'],
            ['2024-01-03', '测试标题3', 'http://test3.com', '测试内容3'],
        ]
        
        test_dir = "test_output"
        os.makedirs(test_dir, exist_ok=True)
        
        # 创建ExcelWriter实例
        excel_writer = ExcelWriter(max_workers=2, default_batch_size=2)
        print(f"✅ ExcelWriter实例创建成功")
        
        # 测试1: 直接写入模式
        print("\n📝 测试1: 直接写入模式")
        file1 = os.path.join(test_dir, "test_direct_mode.xlsx")
        if os.path.exists(file1):
            os.remove(file1)
        
        start_time = time.time()
        for i, row in enumerate(test_data):
            success = excel_writer.write_direct(file1, row, headers if i == 0 else None)
            if not success:
                print(f"❌ 直接写入失败: 第{i+1}行")
                return False
        
        direct_time = time.time() - start_time
        print(f"   直接写入完成，耗时: {direct_time:.3f}秒")
        
        # 测试2: 批量写入模式
        print("\n📦 测试2: 批量写入模式")
        file2 = os.path.join(test_dir, "test_batch_mode.xlsx")
        if os.path.exists(file2):
            os.remove(file2)
        
        start_time = time.time()
        success = excel_writer.write_batch(file2, test_data, headers)
        batch_time = time.time() - start_time
        
        if success:
            print(f"   批量写入完成，耗时: {batch_time:.3f}秒")
        else:
            print(f"❌ 批量写入失败")
            return False
        
        # 测试3: 智能批量写入模式
        print("\n🧠 测试3: 智能批量写入模式")
        file3 = os.path.join(test_dir, "test_smart_mode.xlsx")
        if os.path.exists(file3):
            os.remove(file3)
        
        start_time = time.time()
        for i, row in enumerate(test_data):
            success = excel_writer.write_smart(file3, row, headers if i == 0 else None, batch_size=2)
            if not success:
                print(f"❌ 智能写入失败: 第{i+1}行")
                return False
        
        # 刷新缓存
        excel_writer.flush_cache(file3)
        smart_time = time.time() - start_time
        print(f"   智能批量写入完成，耗时: {smart_time:.3f}秒")
        
        # 测试4: 混合策略模式
        print("\n🔄 测试4: 混合策略模式")
        file4 = os.path.join(test_dir, "test_hybrid_mode.xlsx")
        if os.path.exists(file4):
            os.remove(file4)
        
        start_time = time.time()
        for i, row in enumerate(test_data):
            success = excel_writer.write_hybrid(file4, row, headers if i == 0 else None, threshold=3)  # 设置为3，确保所有数据都写入后才转换
            if not success:
                print(f"❌ 混合策略写入失败: 第{i+1}行")
                return False

        # 完成混合文件转换
        converted_count = excel_writer.finalize_hybrid_files(test_dir)
        hybrid_time = time.time() - start_time
        print(f"   混合策略写入完成，耗时: {hybrid_time:.3f}秒，转换文件: {converted_count}个")
        
        # 测试5: 统一接口
        print("\n🔧 测试5: 统一接口")
        file5 = os.path.join(test_dir, "test_unified_interface.xlsx")
        if os.path.exists(file5):
            os.remove(file5)
        
        start_time = time.time()
        for i, row in enumerate(test_data):
            success = excel_writer.write(file5, row, headers if i == 0 else None, ExcelWriteMode.SMART, batch_size=2)
            if not success:
                print(f"❌ 统一接口写入失败: 第{i+1}行")
                return False
        
        excel_writer.flush_cache(file5)
        unified_time = time.time() - start_time
        print(f"   统一接口写入完成，耗时: {unified_time:.3f}秒")
        
        # 验证文件内容
        print("\n🔍 验证文件内容:")
        test_files = [
            (file1, "直接写入"),
            (file2, "批量写入"),
            (file3, "智能批量"),
            (file4, "混合策略"),
            (file5, "统一接口")
        ]
        
        for filepath, mode_name in test_files:
            if os.path.exists(filepath):
                try:
                    import openpyxl
                    wb = openpyxl.load_workbook(filepath)
                    ws = wb.active
                    
                    row_count = ws.max_row
                    expected_rows = len(test_data) + 1  # +1 for headers
                    
                    if row_count == expected_rows:
                        print(f"   ✅ {mode_name}: 行数正确 ({row_count}行)")
                    else:
                        print(f"   ❌ {mode_name}: 行数错误 (期望{expected_rows}行，实际{row_count}行)")
                        return False
                        
                except Exception as e:
                    print(f"   ❌ {mode_name}: 验证失败 - {e}")
                    return False
            else:
                print(f"   ❌ {mode_name}: 文件不存在")
                return False
        
        # 性能对比
        print(f"\n📊 性能对比:")
        print(f"   直接写入: {direct_time:.3f}秒")
        print(f"   批量写入: {batch_time:.3f}秒")
        print(f"   智能批量: {smart_time:.3f}秒")
        print(f"   混合策略: {hybrid_time:.3f}秒")
        print(f"   统一接口: {unified_time:.3f}秒")
        
        # 清理资源
        excel_writer.cleanup()
        print(f"\n✅ ExcelWriter资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_excel_writer():
    """测试异步Excel写入功能"""
    print("\n🔄 测试异步Excel写入功能")
    print("=" * 60)
    
    try:
        from excel_writer import get_excel_writer, ExcelWriteMode
        
        excel_writer = get_excel_writer()
        
        # 测试数据
        headers = ['ID', '异步测试', '时间戳']
        test_data = [
            [1, '异步测试1', time.time()],
            [2, '异步测试2', time.time()],
            [3, '异步测试3', time.time()],
        ]
        
        test_dir = "test_output"
        file_path = os.path.join(test_dir, "test_async_excel.xlsx")
        if os.path.exists(file_path):
            os.remove(file_path)
        
        start_time = time.time()
        
        # 异步写入测试
        for i, row in enumerate(test_data):
            success = await excel_writer.write_async(
                file_path, row, headers if i == 0 else None, ExcelWriteMode.SMART
            )
            if not success:
                print(f"❌ 异步写入失败: 第{i+1}行")
                return False
        
        # 刷新缓存
        excel_writer.flush_cache(file_path)
        async_time = time.time() - start_time
        
        print(f"✅ 异步写入完成，耗时: {async_time:.3f}秒")
        
        # 验证文件
        if os.path.exists(file_path):
            import openpyxl
            wb = openpyxl.load_workbook(file_path)
            ws = wb.active
            
            row_count = ws.max_row
            expected_rows = len(test_data) + 1
            
            if row_count == expected_rows:
                print(f"✅ 异步写入验证通过: {row_count}行")
                return True
            else:
                print(f"❌ 异步写入验证失败: 期望{expected_rows}行，实际{row_count}行")
                return False
        else:
            print(f"❌ 异步写入文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_global_excel_writer():
    """测试全局Excel写入器"""
    print("\n🌐 测试全局Excel写入器")
    print("=" * 60)
    
    try:
        from excel_writer import get_excel_writer
        
        # 获取两次全局实例，应该是同一个对象
        writer1 = get_excel_writer()
        writer2 = get_excel_writer()
        
        if writer1 is writer2:
            print("✅ 全局Excel写入器单例模式正常")
            return True
        else:
            print("❌ 全局Excel写入器单例模式失败")
            return False
            
    except Exception as e:
        print(f"❌ 全局写入器测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔧 ExcelWriter类功能测试")
    print("=" * 80)
    
    tests = [
        ("ExcelWriter类基本功能", test_excel_writer_class),
        ("全局Excel写入器", test_global_excel_writer),
        ("异步Excel写入", test_async_excel_writer),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
                
            if result:
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！ExcelWriter类功能正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
