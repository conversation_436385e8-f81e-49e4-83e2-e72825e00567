#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试URL去重和缓存功能的集成测试
验证crawl_articles_async函数中的URL去重和缓存保存功能
"""

import asyncio
import sys
import os
import time

# 添加项目根目录到路径
sys.path.append('.')

from core.crawler import crawl_articles_async, deduplicate_articles_by_url, save_collected_urls_to_csv, load_cached_urls_from_csv

async def test_url_dedup_and_cache_integration():
    """测试URL去重和缓存功能的集成"""
    
    print("=" * 70)
    print("🧪 测试URL去重和缓存功能集成")
    print("=" * 70)
    
    # 模拟文章数据（包含重复URL）
    mock_articles = [
        ("文章1", "https://example.com/article1.html", "articles", "测试页面", "https://example.com/list.html", "test"),
        ("文章2", "https://example.com/article2.html", "articles", "测试页面", "https://example.com/list.html", "test"),
        ("文章3", "https://example.com/article3.html", "articles", "测试页面", "https://example.com/list.html", "test"),
        ("重复文章1", "https://example.com/article1.html", "articles", "测试页面", "https://example.com/list.html", "test"),  # 重复
        ("文章4", "https://example.com/article4.html", "articles", "测试页面", "https://example.com/list.html", "test"),
        ("重复文章2", "https://example.com/article2.html", "articles", "测试页面", "https://example.com/list.html", "test"),  # 重复
        ("文章5", "https://example.com/article5.html", "articles", "测试页面", "https://example.com/list.html", "test"),
    ]
    
    test_input_url = "https://example.com/list.html"
    test_config_group = "URL去重缓存测试"
    
    def log_callback(message):
        print(f"📝 {message}")
    
    print(f"1. 测试数据准备")
    print(f"   原始文章数: {len(mock_articles)}")
    print(f"   预期去重后: 5篇文章")
    print(f"   测试URL: {test_input_url}")
    print(f"   配置组: {test_config_group}")
    
    # 显示原始文章列表
    print(f"\n📋 原始文章列表:")
    for i, article in enumerate(mock_articles, 1):
        print(f"   {i}. {article[0]} - {article[1]}")
    
    print(f"\n2. 测试URL去重功能")
    
    # 单独测试去重功能
    deduplicated = deduplicate_articles_by_url(mock_articles, log_callback)
    print(f"   去重结果: {len(mock_articles)} → {len(deduplicated)} 篇文章")
    
    print(f"\n📋 去重后文章列表:")
    for i, article in enumerate(deduplicated, 1):
        print(f"   {i}. {article[0]} - {article[1]}")
    
    print(f"\n3. 测试集成的爬取流程（包含去重和缓存）")
    
    try:
        # 调用crawl_articles_async，传入预处理的文章列表
        # 这将触发URL去重和缓存保存
        result = await crawl_articles_async(
            all_articles=mock_articles,
            input_url=test_input_url,
            config_group=test_config_group,
            log_callback=log_callback,
            export_filename="test_dedup_cache_result",
            file_format="CSV",
            content_selectors=[".content", ".article-content"],  # 模拟选择器
            title_selectors=[".title", "h1"],
            date_selectors=[".date", ".publish-time"],
            source_selectors=[".source", ".author"],
            use_module_config=False  # 禁用模组配置以避免干扰
        )
        
        print(f"\n📊 爬取处理结果:")
        print(f"   总计处理: {result.get('total', 0)} 篇文章")
        print(f"   成功处理: {result.get('success', 0)} 篇文章")
        print(f"   失败处理: {result.get('failed', 0)} 篇文章")
        
        # 验证去重是否正确
        expected_count = 5  # 预期去重后应该有5篇文章
        if result.get('total', 0) == expected_count:
            print(f"   ✅ URL去重功能正常: 预期 {expected_count} 篇，实际 {result.get('total', 0)} 篇")
        else:
            print(f"   ❌ URL去重功能异常: 预期 {expected_count} 篇，实际 {result.get('total', 0)} 篇")
            return False
        
        print(f"\n4. 验证URL缓存保存功能")
        
        # 检查是否生成了缓存文件
        from core.crawler import check_url_cache_exists
        
        cache_exists, cache_path, cache_info = check_url_cache_exists(test_input_url, test_config_group)
        
        if cache_exists:
            print(f"   ✅ 缓存文件已生成: {cache_info['filename']}")
            print(f"   文件路径: {cache_path}")
            print(f"   文件大小: {cache_info['size']} bytes")
            print(f"   修改时间: {cache_info['modified_str']}")
            
            # 验证缓存内容
            cached_urls = load_cached_urls_from_csv(test_input_url, test_config_group, log_callback)
            
            if len(cached_urls) == expected_count:
                print(f"   ✅ 缓存内容正确: {len(cached_urls)} 个URL")
                
                print(f"\n📋 缓存的URL列表:")
                for i, url in enumerate(cached_urls, 1):
                    print(f"   {i}. {url}")
                    
            else:
                print(f"   ❌ 缓存内容异常: 预期 {expected_count} 个URL，实际 {len(cached_urls)} 个")
                return False
                
        else:
            print(f"   ❌ 缓存文件未生成")
            return False
        
        print(f"\n5. 测试完成")
        print(f"✅ URL去重和缓存功能集成测试通过！")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_cache_loading():
    """测试缓存加载功能"""
    
    print(f"\n" + "=" * 70)
    print("🧪 测试缓存加载功能")
    print("=" * 70)
    
    test_input_url = "https://example.com/list.html"
    test_config_group = "URL去重缓存测试"
    
    def log_callback(message):
        print(f"📝 {message}")
    
    try:
        # 测试从缓存加载URL
        print(f"1. 从缓存加载URL")
        cached_urls = load_cached_urls_from_csv(test_input_url, test_config_group, log_callback)
        
        if cached_urls:
            print(f"   ✅ 成功从缓存加载 {len(cached_urls)} 个URL")
            
            # 将缓存的URL转换为文章格式
            cached_articles = []
            for i, url in enumerate(cached_urls):
                cached_articles.append((
                    f"缓存文章{i+1}",  # title
                    url,               # url
                    "articles",        # save_dir
                    "缓存测试",        # page_title
                    test_input_url,    # page_url
                    "cache_test"       # classid
                ))
            
            print(f"\n2. 使用缓存数据进行爬取")
            
            # 使用缓存的文章数据进行爬取
            result = await crawl_articles_async(
                all_articles=cached_articles,
                input_url=test_input_url,
                config_group=test_config_group + "_reload",
                log_callback=log_callback,
                export_filename="test_cache_reload_result",
                file_format="CSV",
                content_selectors=[".content"],
                title_selectors=[".title"],
                use_module_config=False
            )
            
            print(f"\n📊 缓存数据爬取结果:")
            print(f"   总计处理: {result.get('total', 0)} 篇文章")
            print(f"   成功处理: {result.get('success', 0)} 篇文章")
            print(f"   失败处理: {result.get('failed', 0)} 篇文章")
            
            if result.get('total', 0) == len(cached_urls):
                print(f"   ✅ 缓存加载功能正常")
                return True
            else:
                print(f"   ❌ 缓存加载功能异常")
                return False
                
        else:
            print(f"   ❌ 未能从缓存加载URL")
            return False
            
    except Exception as e:
        print(f"❌ 缓存加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    async def main():
        """主测试函数"""
        print("🚀 开始URL去重和缓存功能集成测试")
        
        # 创建测试输出目录
        os.makedirs("test_output", exist_ok=True)
        
        # 测试1：URL去重和缓存保存
        test1_result = await test_url_dedup_and_cache_integration()
        
        # 测试2：缓存加载
        test2_result = await test_cache_loading()
        
        print(f"\n" + "=" * 70)
        print("📊 测试总结")
        print("=" * 70)
        print(f"URL去重和缓存保存测试: {'✅ 通过' if test1_result else '❌ 失败'}")
        print(f"缓存加载测试: {'✅ 通过' if test2_result else '❌ 失败'}")
        
        if test1_result and test2_result:
            print(f"\n🎉 所有测试通过！URL去重和缓存功能工作正常。")
            print(f"\n💡 功能说明:")
            print(f"   - URL去重：自动移除重复的文章URL")
            print(f"   - 缓存保存：将处理的URL保存到CSV文件")
            print(f"   - 缓存加载：从CSV文件加载之前保存的URL")
            print(f"   - 统一处理：无论文章来源如何，都会执行去重和缓存")
        else:
            print(f"\n❌ 部分测试失败，请检查相关功能。")
    
    # 运行测试
    asyncio.run(main())
