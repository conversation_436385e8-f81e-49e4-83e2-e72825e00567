#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL缓存功能测试脚本
"""

import asyncio
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.crawler import (
    save_collected_urls_to_csv,
    load_cached_urls_from_csv,
    check_url_cache_exists
)


def test_url_cache_functions():
    """测试URL缓存相关函数"""
    print("🧪 开始测试URL缓存功能...")
    
    # 测试数据
    test_urls = [
        "https://www.example.com/news/article1.html",
        "https://www.example.com/news/article2.html",
        "https://www.example.com/news/article3.html",
        "https://www.example.com/news/article4.html",
        "https://www.example.com/news/article5.html"
    ]
    
    test_input_url = "https://www.example.com/news/list.html"
    test_config_group = "测试配置组"
    
    def log_callback(message):
        print(f"📝 {message}")
    
    print("\n1️⃣ 测试保存URL到CSV...")
    
    # 测试保存功能
    saved_path = save_collected_urls_to_csv(
        urls=test_urls,
        input_url=test_input_url,
        config_group=test_config_group,
        log_callback=log_callback
    )
    
    if saved_path:
        print(f"✅ 保存成功: {saved_path}")
    else:
        print("❌ 保存失败")
        return False
    
    print("\n2️⃣ 测试检查缓存是否存在...")
    
    # 测试检查缓存
    exists, filepath, info = check_url_cache_exists(
        input_url=test_input_url,
        config_group=test_config_group
    )
    
    if exists:
        print(f"✅ 缓存存在: {info['filename']}")
        print(f"   文件大小: {info['size']} bytes")
        print(f"   修改时间: {info['modified_str']}")
    else:
        print("❌ 缓存不存在")
        return False
    
    print("\n3️⃣ 测试从CSV加载URL...")
    
    # 测试加载功能
    loaded_urls = load_cached_urls_from_csv(
        input_url=test_input_url,
        config_group=test_config_group,
        log_callback=log_callback
    )
    
    if loaded_urls:
        print(f"✅ 加载成功: {len(loaded_urls)} 个URL")
        
        # 验证数据一致性
        if set(loaded_urls) == set(test_urls):
            print("✅ 数据一致性验证通过")
        else:
            print("❌ 数据一致性验证失败")
            print(f"   原始: {test_urls}")
            print(f"   加载: {loaded_urls}")
            return False
    else:
        print("❌ 加载失败")
        return False
    
    print("\n4️⃣ 测试不存在的缓存...")
    
    # 测试不存在的缓存
    exists2, _, _ = check_url_cache_exists(
        input_url="https://nonexistent.com/list.html",
        config_group=test_config_group
    )
    
    if not exists2:
        print("✅ 正确识别不存在的缓存")
    else:
        print("❌ 错误识别了不存在的缓存")
        return False
    
    print("\n🎉 所有测试通过！URL缓存功能正常工作。")
    return True


async def test_crawl_with_cache():
    """测试爬虫函数的缓存功能"""
    print("\n🕷️ 测试爬虫缓存功能...")
    
    # 注意：这里只是演示如何调用，实际测试需要真实的网站URL
    print("💡 提示：要测试完整的爬虫缓存功能，请使用真实的网站URL")
    print("   示例调用方式：")
    print("""
    from core.crawler import crawl_articles_async
    
    # 第一次爬取（建立缓存）
    result1 = await crawl_articles_async(
        input_url="https://your-website.com/news/list.html",
        config_group="测试组",
        skip_pagination_if_cached=False,  # 第一次不跳过
        max_pages=2,
        headless=True
    )
    
    # 第二次爬取（使用缓存）
    result2 = await crawl_articles_async(
        input_url="https://your-website.com/news/list.html",
        config_group="测试组", 
        skip_pagination_if_cached=True,   # 启用跳过翻页
        max_pages=2,
        headless=True
    )
    """)


def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_dir = "url_cache/测试配置组"
    if os.path.exists(test_dir):
        import shutil
        try:
            shutil.rmtree("url_cache")
            print("✅ 测试文件清理完成")
        except Exception as e:
            print(f"⚠️ 清理测试文件时出错: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 URL缓存功能测试")
    print("=" * 60)
    
    try:
        # 测试基础功能
        if test_url_cache_functions():
            print("\n✅ 基础功能测试通过")
        else:
            print("\n❌ 基础功能测试失败")
            return
        
        # 测试爬虫集成
        asyncio.run(test_crawl_with_cache())
        
        print("\n" + "=" * 60)
        print("🎯 测试总结:")
        print("✅ URL缓存保存功能正常")
        print("✅ URL缓存加载功能正常") 
        print("✅ 缓存检测功能正常")
        print("✅ 数据一致性验证通过")
        print("💡 可以开始使用新的URL缓存功能了！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 询问是否清理测试文件
        try:
            response = input("\n是否清理测试文件？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                cleanup_test_files()
        except KeyboardInterrupt:
            print("\n测试结束")


if __name__ == "__main__":
    main()
