#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试失败URL处理功能
"""

import os
import csv
import openpyxl

def create_test_files():
    """创建测试用的失败URL文件"""
    print("🔧 创建测试文件...")
    
    # 测试URL列表
    test_urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json", 
        "https://example.com",
        "https://httpbin.org/status/200"
    ]
    
    # 创建CSV文件
    csv_file = "test_failed_urls.csv"
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(["URL", "Title", "Status", "Error"])
        for i, url in enumerate(test_urls):
            writer.writerow([url, f"Test Title {i+1}", "Failed", "Test error"])
    
    print(f"✅ 创建CSV文件: {csv_file}")
    
    # 创建Excel文件
    excel_file = "test_failed_urls.xlsx"
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.append(["URL", "Title", "Status", "Error"])
    for i, url in enumerate(test_urls):
        ws.append([url, f"Test Title {i+1}", "Failed", "Test error"])
    wb.save(excel_file)
    
    print(f"✅ 创建Excel文件: {excel_file}")
    
    return csv_file, excel_file

def test_url_extraction():
    """测试URL提取功能"""
    print("\n🔍 测试URL提取功能...")
    
    csv_file, excel_file = create_test_files()
    
    # 测试CSV提取
    print("测试CSV文件URL提取:")
    csv_urls = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader, [])
        
        # 查找URL列
        url_column = 0
        for i, header in enumerate(headers):
            if 'url' in header.lower():
                url_column = i
                break
        
        print(f"  URL列索引: {url_column}")
        
        # 读取URL
        for row in reader:
            if len(row) > url_column and row[url_column].strip():
                csv_urls.append(row[url_column].strip())
    
    print(f"  提取到 {len(csv_urls)} 个URL: {csv_urls}")
    
    # 测试Excel提取
    print("测试Excel文件URL提取:")
    excel_urls = []
    wb = openpyxl.load_workbook(excel_file)
    ws = wb.active
    
    # 查找URL列
    url_column = None
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row=1, column=col).value
        if cell_value and 'url' in str(cell_value).lower():
            url_column = col
            break
    
    if url_column is None:
        url_column = 1  # 默认第一列
    
    print(f"  URL列索引: {url_column}")
    
    # 读取URL
    for row in range(2, ws.max_row + 1):
        url = ws.cell(row=row, column=url_column).value
        if url and str(url).strip():
            excel_urls.append(str(url).strip())
    
    print(f"  提取到 {len(excel_urls)} 个URL: {excel_urls}")
    
    return csv_urls, excel_urls

def test_crawler_import():
    """测试crawler模块导入"""
    print("\n🔍 测试crawler模块导入...")
    
    try:
        from crawler import save_article
        print("✅ save_article 导入成功")
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(save_article)
        params = list(sig.parameters.keys())
        print(f"📋 save_article 参数: {params}")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_single_url_processing():
    """测试单个URL处理"""
    print("\n🔍 测试单个URL处理...")
    
    try:
        from crawler import save_article
        
        # 测试参数
        test_url = "https://httpbin.org/html"
        save_dir = "test_output"
        
        # 创建保存目录
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        print(f"🔄 测试处理URL: {test_url}")
        
        result = save_article(
            link=test_url,
            save_dir=save_dir,
            page_title="测试处理",
            title_selectors=["h1", ".title"],
            content_selectors=[".content", "body"],
            date_selectors=[".date"],
            source_selectors=[".source"],
            title_selector_type="CSS",
            content_type="CSS",
            date_selector_type="CSS",
            source_selector_type="CSS",
            export_filename="test_result",
            file_format="CSV",
            retry=1,
            use_module_config=False
        )
        
        if result:
            print("✅ URL处理成功")
            return True
        else:
            print("❌ URL处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_config():
    """测试GUI配置获取"""
    print("\n🔍 测试GUI配置...")
    
    try:
        # 模拟GUI配置
        mock_config = {
            'title_selector': 'h1, .title, #title',
            'content_selectors': '.content, #content, .article-content',
            'date_selector': '.date, .time, .publish-time',
            'source_selector': '.source, .author',
            'title_selector_type': 'CSS',
            'content_type': 'CSS',
            'date_selector_type': 'CSS',
            'source_selector_type': 'CSS'
        }
        
        # 测试选择器解析
        title_selectors = [s.strip() for s in mock_config.get('title_selector', 'h1').split(',') if s.strip()]
        content_selectors = [s.strip() for s in mock_config.get('content_selectors', '.content').split(',') if s.strip()]
        
        print(f"📋 标题选择器: {title_selectors}")
        print(f"📋 内容选择器: {content_selectors}")
        
        if title_selectors and content_selectors:
            print("✅ 配置解析成功")
            return True
        else:
            print("❌ 配置解析失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试出错: {e}")
        return False

def main():
    """主测试函数"""
    print("失败URL处理功能测试")
    print("=" * 50)
    
    tests = [
        ("URL提取功能", test_url_extraction),
        ("crawler模块导入", test_crawler_import),
        ("GUI配置测试", test_gui_config),
        ("单个URL处理", test_single_url_processing),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！失败URL处理功能正常。")
        print("\n可以使用的测试文件:")
        print("- test_failed_urls.csv")
        print("- test_failed_urls.xlsx")
        print("\n在GUI中选择这些文件进行测试。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")
        
        # 提供调试建议
        print("\n🔧 调试建议:")
        print("1. 确保crawler.py文件存在且可导入")
        print("2. 检查save_article函数的参数是否正确")
        print("3. 确保openpyxl模块已安装（pip install openpyxl）")
        print("4. 检查文件路径和权限")

if __name__ == "__main__":
    main()
