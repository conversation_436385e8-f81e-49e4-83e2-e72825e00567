#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from core.txt_clear import enhanced_content_filter

# 测试文本
test_text = """<div>
<h1>测试标题</h1>

<p>第一段内容。</p>

<p>第二段内容。</p>

<div class="meta">发布时间：2025-01-11</div>

<p>第三段内容。</p>
</div>"""

print("原始文本:")
print("-" * 50)
print(repr(test_text))  # 使用repr显示真实的换行符
print("-" * 50)
print("显示效果:")
print(test_text)
print("-" * 50)
print(f"换行符数量: {test_text.count(chr(10))}")  # 使用chr(10)表示换行符

# 处理文本
result = enhanced_content_filter(test_text)

print("\n处理后文本:")
print("-" * 50)
print(repr(result))  # 使用repr显示真实的换行符
print("-" * 50)
print("显示效果:")
print(result)
print("-" * 50)
print(f"换行符数量: {result.count(chr(10))}")

# 验证结果
if result.count(chr(10)) > 0:
    print("✅ 换行符保留成功!")
else:
    print("❌ 换行符未保留")

# 检查内容保留
expected_content = ["测试标题", "第一段内容", "第二段内容", "第三段内容"]
preserved = [content for content in expected_content if content in result]
print(f"保留的内容: {len(preserved)}/{len(expected_content)}")

# 检查无用内容清除
if "发布时间" not in result:
    print("✅ 无用内容已清除")
else:
    print("❌ 无用内容未清除")

print("\n测试完成!")
