#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试失败URL处理修复
"""

import os
import csv
import openpyxl

def create_simple_test_file():
    """创建简单的测试文件"""
    print("🔧 创建测试文件...")
    
    # 创建测试Excel文件
    wb = openpyxl.Workbook()
    ws = wb.active
    
    # 添加表头
    ws.append(["URL", "标题", "状态", "错误信息"])
    
    # 添加测试URL
    test_urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://example.com"
    ]
    
    for i, url in enumerate(test_urls):
        ws.append([url, f"测试标题{i+1}", "失败", "测试错误"])
    
    test_file = "test_simple_failed.xlsx"
    wb.save(test_file)
    
    print(f"✅ 创建测试文件: {test_file}")
    print(f"📁 文件路径: {os.path.abspath(test_file)}")
    
    # 显示文件内容
    print("\n📋 文件内容:")
    wb = openpyxl.load_workbook(test_file)
    ws = wb.active
    
    for row in range(1, ws.max_row + 1):
        row_data = []
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=row, column=col).value
            row_data.append(str(cell_value) if cell_value else "")
        print(f"  第{row}行: {row_data}")
    
    return test_file

def test_url_extraction(file_path):
    """测试URL提取"""
    print(f"\n🔍 测试URL提取: {file_path}")
    
    try:
        wb = openpyxl.load_workbook(file_path)
        ws = wb.active
        
        print(f"📊 文件信息: {ws.max_row}行, {ws.max_column}列")
        
        # 查找URL列
        url_column = None
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=1, column=col).value
            print(f"第{col}列表头: {cell_value}")
            if cell_value and ('url' in str(cell_value).lower() or 'link' in str(cell_value).lower()):
                url_column = col
                print(f"🔍 找到URL列: 第{col}列")
                break
        
        if url_column is None:
            url_column = 1
            print("⚠️ 未找到URL列，使用第1列")
        
        # 提取URL
        urls = []
        for row in range(2, ws.max_row + 1):
            url = ws.cell(row=row, column=url_column).value
            if url and str(url).strip():
                urls.append(str(url).strip())
                print(f"📎 提取URL: {str(url).strip()}")
        
        print(f"✅ 共提取到 {len(urls)} 个URL")
        return urls
        
    except Exception as e:
        print(f"❌ 提取失败: {e}")
        return []

def test_simple_processing():
    """测试简单的URL处理"""
    print("\n🔍 测试简单URL处理...")
    
    test_url = "https://httpbin.org/html"
    
    try:
        import requests
        from bs4 import BeautifulSoup
        
        print(f"🌐 访问: {test_url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(test_url, headers=headers, timeout=10)
        print(f"📡 响应状态: {response.status_code}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 尝试提取标题
        title_selectors = ['h1', 'title', '.title']
        title = "未找到标题"
        
        for selector in title_selectors:
            try:
                element = soup.select_one(selector)
                if element and element.get_text(strip=True):
                    title = element.get_text(strip=True)
                    print(f"📝 找到标题: {title}")
                    break
            except:
                continue
        
        # 尝试提取内容
        content_selectors = ['body', '.content', '#content']
        content = "未找到内容"
        
        for selector in content_selectors:
            try:
                element = soup.select_one(selector)
                if element and element.get_text(strip=True):
                    content = element.get_text(strip=True)[:200]
                    print(f"📄 找到内容: {content[:100]}...")
                    break
            except:
                continue
        
        print("✅ URL处理测试成功")
        return True
        
    except Exception as e:
        print(f"❌ URL处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("失败URL处理修复测试")
    print("=" * 40)
    
    # 创建测试文件
    test_file = create_simple_test_file()
    
    # 测试URL提取
    urls = test_url_extraction(test_file)
    
    # 测试简单处理
    processing_ok = test_simple_processing()
    
    print("\n" + "=" * 40)
    print("📋 测试总结:")
    print(f"✅ 测试文件创建: {test_file}")
    print(f"✅ URL提取: {len(urls)} 个URL")
    print(f"✅ URL处理: {'成功' if processing_ok else '失败'}")
    
    if urls and processing_ok:
        print("\n🎉 基础功能测试通过！")
        print("\n🎯 现在可以在GUI中测试:")
        print(f"1. 选择文件: {test_file}")
        print("2. 设置保存目录")
        print("3. 点击'处理失败URL'")
        print("4. 查看详细日志输出")
    else:
        print("\n⚠️ 测试未完全通过，请检查相关组件")

if __name__ == "__main__":
    main()
