#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫项目主入口
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    from gui.main_window import main as gui_main
    gui_main()

if __name__ == "__main__":
    # 测试导入是否正常
    try:
        main()

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
