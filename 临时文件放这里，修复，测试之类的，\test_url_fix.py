#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试URL过度清洗修复效果
"""

import os
import sys

# 添加core目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'utils'))

def test_url_deduplication():
    """测试URL去重功能"""
    print("🔍 测试URL去重功能修复")
    print("=" * 50)
    
    try:
        from crawler import deduplicate_articles_by_url
        from text_cleaner import normalize_url_for_deduplication
        
        print("✅ 函数导入成功")
        
        # 测试元组格式的文章数据（实际使用的格式）
        test_articles = [
            ('姚晓红副主任视察部分选区人大代表补选投票情况', 'https://www.shrd.gov.cn/n8347/n8379/n8380', 'save_dir1', 'page_title1', 'page_url1', 'classid1'),
            ('另一篇文章标题', 'https://www.shrd.gov.cn/n8347/n8379/n8381', 'save_dir2', 'page_title2', 'page_url2', 'classid2'),
            ('重复的文章', 'https://www.shrd.gov.cn/n8347/n8379/n8380', 'save_dir3', 'page_title3', 'page_url3', 'classid3'),  # 重复URL
        ]
        
        print(f"📝 测试数据: {len(test_articles)} 篇文章")
        for i, article in enumerate(test_articles, 1):
            print(f"   {i}. 标题: {article[0][:30]}...")
            print(f"      URL: {article[1]}")
        
        # 执行去重
        deduplicated = deduplicate_articles_by_url(test_articles)
        
        print(f"\n📊 去重结果:")
        print(f"   原始文章数: {len(test_articles)}")
        print(f"   去重后文章数: {len(deduplicated)}")
        print(f"   移除重复数: {len(test_articles) - len(deduplicated)}")
        
        print(f"\n✅ 去重后的文章:")
        for i, article in enumerate(deduplicated, 1):
            print(f"   {i}. 标题: {article[0][:30]}...")
            print(f"      URL: {article[1]}")
        
        # 测试URL规范化
        print(f"\n🔧 测试URL规范化:")
        test_urls = [
            'https://www.shrd.gov.cn/n8347/n8379/n8380',
            'https://example.com/page?utm_source=test&param=value',
            'https://test.com/article#section1',
            'https://site.com/news?from=homepage&timestamp=123456'
        ]
        
        for url in test_urls:
            normalized = normalize_url_for_deduplication(url)
            print(f"   原始: {url}")
            print(f"   规范: {normalized}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_processing():
    """测试URL处理相关功能"""
    print("\n🌐 测试URL处理功能")
    print("=" * 50)
    
    try:
        from crawler import get_full_link
        
        # 测试URL拼接
        test_cases = [
            {
                'href': '/n8347/n8379/n8380',
                'input_url': 'https://www.shrd.gov.cn',
                'base_url': 'https://www.shrd.gov.cn',
                'url_mode': 'absolute',
                'expected': 'https://www.shrd.gov.cn/n8347/n8379/n8380'
            },
            {
                'href': 'https://www.shrd.gov.cn/n8347/n8379/n8380',
                'input_url': 'https://www.shrd.gov.cn',
                'base_url': 'https://www.shrd.gov.cn',
                'url_mode': 'absolute',
                'expected': 'https://www.shrd.gov.cn/n8347/n8379/n8380'
            },
            {
                'href': '../news/article.html',
                'input_url': 'https://example.com/category/list.html',
                'base_url': 'https://example.com',
                'url_mode': 'absolute',
                'expected': 'https://example.com/news/article.html'
            }
        ]
        
        print("🔗 测试URL拼接:")
        for i, case in enumerate(test_cases, 1):
            result = get_full_link(
                case['href'], 
                case['input_url'], 
                case['base_url'], 
                case['url_mode']
            )
            
            print(f"\n   测试 {i}:")
            print(f"     href: {case['href']}")
            print(f"     input_url: {case['input_url']}")
            print(f"     结果: {result}")
            print(f"     期望: {case['expected']}")
            
            if result == case['expected']:
                print(f"     ✅ 通过")
            else:
                print(f"     ❌ 失败")
        
        return True
        
    except Exception as e:
        print(f"❌ URL处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_no_url_articles():
    """测试无URL文章的处理"""
    print("\n⚠️ 测试无URL文章处理")
    print("=" * 50)
    
    try:
        from crawler import deduplicate_articles_by_url
        
        # 测试包含无URL文章的数据
        test_articles = [
            ('正常文章', 'https://example.com/article1', 'dir1', 'title1', 'url1', 'class1'),
            ('无URL文章', '', 'dir2', 'title2', 'url2', 'class2'),  # 空URL
            ('另一篇正常文章', 'https://example.com/article2', 'dir3', 'title3', 'url3', 'class3'),
        ]
        
        print("📝 测试数据（包含无URL文章）:")
        for i, article in enumerate(test_articles, 1):
            url = article[1] if article[1] else "【无URL】"
            print(f"   {i}. {article[0]} - {url}")
        
        # 执行去重（应该保留无URL文章并记录警告）
        deduplicated = deduplicate_articles_by_url(test_articles)
        
        print(f"\n📊 处理结果:")
        print(f"   原始文章数: {len(test_articles)}")
        print(f"   处理后文章数: {len(deduplicated)}")
        
        print(f"\n✅ 处理后的文章:")
        for i, article in enumerate(deduplicated, 1):
            url = article[1] if article[1] else "【无URL】"
            print(f"   {i}. {article[0]} - {url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 无URL文章测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 URL过度清洗修复测试")
    print("=" * 60)
    
    tests = [
        ("URL去重功能", test_url_deduplication),
        ("URL处理功能", test_url_processing),
        ("无URL文章处理", test_no_url_articles),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！URL过度清洗问题已修复")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
