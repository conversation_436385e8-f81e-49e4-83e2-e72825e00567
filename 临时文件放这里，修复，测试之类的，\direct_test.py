#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_newline_preservation():
    print("开始测试换行符保留功能...")
    
    try:
        # 直接导入函数
        from core.txt_clear import enhanced_content_filter
        
        # 测试文本
        test_input = """<article>
<h1>测试标题</h1>

<p>这是第一段。</p>

<p>这是第二段。</p>

<div class="meta">发布时间：2025-01-11</div>

<p>这是第三段。</p>
</article>"""
        
        print("输入文本:")
        print("=" * 50)
        print(test_input)
        print("=" * 50)
        
        # 处理文本
        result = enhanced_content_filter(test_input)
        
        print("\n输出文本:")
        print("=" * 50)
        print(result)
        print("=" * 50)
        
        print(f"\n统计信息:")
        print(f"输入换行符数量: {test_input.count(chr(10))}")
        print(f"输出换行符数量: {result.count(chr(10))}")
        print(f"输出长度: {len(result)}")
        
        # 检查是否保留了有用内容
        useful_content = ["测试标题", "第一段", "第二段", "第三段"]
        preserved = [content for content in useful_content if content in result]
        print(f"保留的有用内容: {len(preserved)}/{len(useful_content)}")
        
        # 检查是否清除了无用内容
        useless_content = ["发布时间"]
        removed = [content for content in useless_content if content not in result]
        print(f"清除的无用内容: {len(removed)}/{len(useless_content)}")
        
        if result.count('\n') > 0 and len(preserved) >= 3:
            print("✅ 测试通过：换行符已保留，内容正确处理")
            return True
        else:
            print("❌ 测试失败")
            return False
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_newline_preservation()
    print(f"\n测试结果: {'成功' if success else '失败'}")
