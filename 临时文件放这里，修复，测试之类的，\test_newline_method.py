#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from core.txt_clear import enhanced_content_filter

def test_br_to_newline():
    """测试<br>转换为换行符"""
    print("=" * 60)
    print("🧪 测试<br>转换为换行符")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "基本<br>标签",
            "input": "第一行<br>第二行<br>第三行",
            "expected_newlines": 2
        },
        {
            "name": "<br/>自闭合标签",
            "input": "第一行<br/>第二行<br />第三行",
            "expected_newlines": 2
        },
        {
            "name": "混合<br>和<p>标签",
            "input": "<p>段落1</p><p>段落2<br>换行</p><p>段落3</p>",
            "expected_newlines": 3
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        result = enhanced_content_filter(case["input"])
        newline_count = result.count('\n')
        
        print(f"\n测试 {i}: {case['name']}")
        print(f"输入: {case['input']}")
        print(f"输出: {repr(result)}")
        print(f"显示: {result}")
        print(f"换行符数量: {newline_count} (期望: {case['expected_newlines']})")
        
        if newline_count >= case['expected_newlines']:
            print("✅ 通过")
        else:
            print("❌ 失败")


def test_p_to_newline():
    """测试</p>转换为换行符"""
    print("\n" + "=" * 60)
    print("🧪 测试</p>转换为换行符")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "基本段落标签",
            "input": "<p>第一段</p><p>第二段</p><p>第三段</p>",
            "expected_content": ["第一段", "第二段", "第三段"]
        },
        {
            "name": "段落标签带属性",
            "input": '<p class="content">段落1</p><p id="para2">段落2</p>',
            "expected_content": ["段落1", "段落2"]
        },
        {
            "name": "嵌套HTML",
            "input": "<div><p>段落1</p><p>段落2</p></div>",
            "expected_content": ["段落1", "段落2"]
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        result = enhanced_content_filter(case["input"])
        
        print(f"\n测试 {i}: {case['name']}")
        print(f"输入: {case['input']}")
        print(f"输出: {repr(result)}")
        print(f"显示: {result}")
        
        # 检查内容保留
        found_content = [content for content in case['expected_content'] if content in result]
        print(f"内容保留: {len(found_content)}/{len(case['expected_content'])}")
        
        if len(found_content) == len(case['expected_content']):
            print("✅ 通过")
        else:
            print("❌ 失败")


def test_complete_example():
    """测试完整示例"""
    print("\n" + "=" * 60)
    print("🧪 测试完整示例")
    print("=" * 60)
    
    html_content = """<article>
    <h1>重要新闻标题</h1>
    
    <p>这是新闻的第一段内容，包含重要信息。</p>
    
    <p>这是新闻的第二段内容，<br>包含换行符的内容。</p>
    
    <div class="meta">
        发布时间：2025-01-11 14:30:00<br>
        编辑：张三
    </div>
    
    <p>这是新闻的第三段内容。</p>
    
    <div class="footer">
        版权所有 © 2025 新闻网站
    </div>
</article>"""
    
    print("原始HTML内容:")
    print("-" * 40)
    print(html_content)
    print("-" * 40)
    
    result = enhanced_content_filter(html_content)
    
    print("\n处理后内容:")
    print("-" * 40)
    print(repr(result))
    print("-" * 40)
    print("显示效果:")
    print(result)
    print("-" * 40)
    
    # 统计信息
    print(f"\n📊 统计信息:")
    print(f"换行符数量: {result.count(chr(10))}")
    print(f"总长度: {len(result)}")
    
    # 检查内容保留
    expected_content = ["重要新闻标题", "第一段内容", "第二段内容", "第三段内容"]
    found_content = [content for content in expected_content if content in result]
    print(f"保留内容: {len(found_content)}/{len(expected_content)}")
    
    # 检查无用内容清除
    unwanted_content = ["发布时间", "编辑：", "版权所有"]
    found_unwanted = [content for content in unwanted_content if content in result]
    print(f"清除无用内容: {len(unwanted_content) - len(found_unwanted)}/{len(unwanted_content)}")
    
    if (result.count('\n') > 0 and 
        len(found_content) >= 3 and 
        len(found_unwanted) <= 1):
        print("✅ 完整示例测试通过")
        return True
    else:
        print("❌ 完整示例测试失败")
        return False


def main():
    """主测试函数"""
    print("🚀 测试新的换行符保留方法")
    print("方法：<br> → \\n, </p> → \\n, \\n加入过滤白名单")
    
    # 运行各项测试
    test_br_to_newline()
    test_p_to_newline()
    success = test_complete_example()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 新方法测试成功！")
        print("✅ <br>标签正确转换为换行符")
        print("✅ </p>标签正确转换为换行符")
        print("✅ 换行符在过滤过程中得到保留")
        print("✅ 无用内容被正确清除")
        print("✅ 有用内容和格式得到保留")
    else:
        print("❌ 测试失败，需要进一步调整")
    
    return success


if __name__ == "__main__":
    main()
