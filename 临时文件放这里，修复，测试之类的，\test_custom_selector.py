#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义字段选择器功能
验证选择器自定义和测试功能是否正常工作
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_custom_selector_dialog():
    """测试自定义选择器对话框"""
    print("=" * 60)
    print("测试自定义选择器对话框")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.custom_selector_dialog import CustomSelectorDialog
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = CustomSelectorDialog()
        print("✅ 自定义选择器对话框创建成功")
        
        # 检查对话框属性
        has_tab_widget = hasattr(dialog, 'tab_widget')
        has_selector_editors = hasattr(dialog, 'selector_editors')
        
        print(f"📋 标签页控件: {'✅' if has_tab_widget else '❌'}")
        print(f"📋 选择器编辑器: {'✅' if has_selector_editors else '❌'}")
        
        if has_selector_editors:
            print(f"📊 选择器编辑器数量: {len(dialog.selector_editors)}")
            
            # 显示前几个字段
            field_names = list(dialog.selector_editors.keys())[:5]
            print(f"📝 示例字段: {field_names}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_test_dialog():
    """测试选择器测试对话框"""
    print("=" * 60)
    print("测试选择器测试对话框")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.selector_test_dialog import SelectorTestDialog
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试数据
        test_url = "https://httpbin.org/html"
        test_selectors = {
            "title": ["h1", ".title", "title"],
            "content": ["body", ".content", "p"]
        }
        
        # 创建对话框
        dialog = SelectorTestDialog(None, test_url, test_selectors)
        print("✅ 选择器测试对话框创建成功")
        
        # 检查对话框属性
        has_result_area = hasattr(dialog, 'result_area')
        has_progress_bar = hasattr(dialog, 'progress_bar')
        has_test_thread = hasattr(dialog, 'test_thread')
        
        print(f"📋 结果显示区域: {'✅' if has_result_area else '❌'}")
        print(f"📋 进度条: {'✅' if has_progress_bar else '❌'}")
        print(f"📋 测试线程: {'✅' if has_test_thread else '❌'}")
        
        print(f"🔗 测试URL: {dialog.test_url}")
        print(f"📊 测试字段数量: {len(dialog.field_selectors)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_config_modification():
    """测试字段配置修改"""
    print("=" * 60)
    print("测试字段配置修改")
    print("=" * 60)
    
    try:
        from core.field_config_manager import get_field_config_manager
        
        manager = get_field_config_manager()
        print("✅ 字段配置管理器获取成功")
        
        # 获取原始配置
        original_config = manager.get_available_fields()
        print(f"📊 原始字段数量: {len(original_config)}")
        
        # 测试修改likes字段的选择器
        if 'likes' in original_config:
            original_selectors = original_config['likes'].get('selectors', [])
            print(f"📝 likes字段原始选择器: {original_selectors}")
            
            # 添加自定义选择器
            custom_selectors = [".custom-like-count", ".my-likes"] + original_selectors
            
            # 更新配置
            if 'likes' in manager.config_data.get("extended_fields", {}):
                manager.config_data["extended_fields"]["likes"]["selectors"] = custom_selectors
                print(f"✅ 已更新likes字段选择器: {custom_selectors}")
                
                # 验证更新
                updated_config = manager.get_available_fields()
                updated_selectors = updated_config['likes'].get('selectors', [])
                
                if updated_selectors == custom_selectors:
                    print("✅ 选择器更新验证成功")
                else:
                    print("❌ 选择器更新验证失败")
                    return False
                
                # 恢复原始配置
                manager.config_data["extended_fields"]["likes"]["selectors"] = original_selectors
                print("🔄 已恢复原始配置")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_selector_syntax():
    """测试选择器语法"""
    print("=" * 60)
    print("测试选择器语法")
    print("=" * 60)
    
    try:
        from bs4 import BeautifulSoup
        
        # 测试HTML
        test_html = """
        <html>
        <body>
            <div class="article">
                <h1 class="title">测试标题</h1>
                <div class="meta">
                    <span class="like-count" data-likes="123">123 赞</span>
                    <span class="view-count" data-views="456">456 阅读</span>
                    <span class="price">￥99.99</span>
                </div>
                <div class="content">
                    <p>这是测试内容...</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        soup = BeautifulSoup(test_html, 'html.parser')
        print("✅ 测试HTML解析成功")
        
        # 测试不同类型的选择器
        test_cases = [
            ("标题选择器", ["h1", ".title", "h1.title"], "测试标题"),
            ("点赞数选择器", [".like-count", "[data-likes]", ".meta .like-count"], "123 赞"),
            ("阅读量选择器", [".view-count", "[data-views]", ".meta .view-count"], "456 阅读"),
            ("价格选择器", [".price", ".meta .price"], "￥99.99"),
            ("内容选择器", [".content", ".content p", "div.content"], "这是测试内容...")
        ]
        
        for field_name, selectors, expected_text in test_cases:
            print(f"\n🧪 测试 {field_name}:")
            
            for i, selector in enumerate(selectors):
                try:
                    elements = soup.select(selector)
                    if elements:
                        text = elements[0].get_text(strip=True)
                        if expected_text in text:
                            print(f"   ✅ {selector}: 找到匹配 - {text}")
                        else:
                            print(f"   ⚠️ {selector}: 找到元素但内容不匹配 - {text}")
                    else:
                        print(f"   ❌ {selector}: 未找到匹配元素")
                except Exception as e:
                    print(f"   ❌ {selector}: 选择器错误 - {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("=" * 60)
    print("测试GUI集成")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CrawlerGUI
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CrawlerGUI()
        print("✅ 主窗口创建成功")
        
        # 检查自定义选择器相关方法
        has_custom_selector_method = hasattr(window, 'open_custom_selector_dialog')
        has_test_selector_method = hasattr(window, 'test_field_selectors')
        
        print(f"🔧 自定义选择器方法: {'✅' if has_custom_selector_method else '❌'}")
        print(f"🔧 测试选择器方法: {'✅' if has_test_selector_method else '❌'}")
        
        # 检查字段配置标签页
        has_fields_tab = hasattr(window, 'fields_tab')
        print(f"📋 字段配置标签页: {'✅' if has_fields_tab else '❌'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试自定义字段选择器功能")
    print()
    
    tests = [
        ("自定义选择器对话框测试", test_custom_selector_dialog),
        ("选择器测试对话框测试", test_selector_test_dialog),
        ("字段配置修改测试", test_field_config_modification),
        ("选择器语法测试", test_selector_syntax),
        ("GUI集成测试", test_gui_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！自定义字段选择器功能正常。")
    else:
        print("⚠️ 部分测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
